<template>
  <xpopup
    :show="show"
    @close="handleClose"
    @confirm="handleConfirm"
    :showBtn="true"
    title="设备重启">
    <view class="popup-content restart"> 是否确定重启设备? </view>
  </xpopup>
</template>

<script>
import { sendCommand } from "@/api/device/device.js";
import getDict from "@/utils/getDict.js";

export default {
  name: "DeviceRestartModal",
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    deviceId: {
      type: [String, Number],
      required: true,
    },
  },
  methods: {
    getDict,

    handleClose() {
      this.$emit("close");
    },

    async handleConfirm() {
      try {
        let templet = null;
        await this.getDict("mqtt_cmd_templet_task", "system").then((res) => {
          templet = JSON.parse(res[0].value);
          templet.data.task = "reboot";
          templet.data.appId = undefined;
          templet.data.src = undefined;
        });

        await sendCommand([
          {
            deviceId: this.deviceId,
            templet: templet,
          },
        ]);

        this.$modal.showToast("重启成功~");
        this.$emit("success");
        this.$emit("close");
      } catch (error) {
        console.error("设备重启失败:", error);
        this.$modal.showToast("重启失败，请重试");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.popup-content {
  padding: 0 24rpx;
}

.restart {
  padding: 40rpx 0;
  font-size: 34rpx;
  text-align: center;
}
</style>
