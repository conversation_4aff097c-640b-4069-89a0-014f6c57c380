<template>
  <xpopup
    :show="show"
    @close="handleClose"
    @confirm="handleConfirm"
    :showBtn="true"
    title="编辑">
    <view class="popup-content">
      <view class="point-name">
        <view class="pop-item flex align-center">
          <view class="pop-label"> 名称： </view>
          <view class="pop-content">
            <u-input
              v-model="editForm.name"
              maxlength="12"
              placeholder="请输入点位名称"
              border="none">
            </u-input>
          </view>
        </view>
        <view class="pop-item flex align-center">
          <view class="pop-label"> 点位： </view>
          <view
            class="pop-content"
            @click="$tab.navigateTo(`/pages/point/point?type=1`)">
            <u-input
              v-model="editForm.placeName"
              placeholder="请选择点位"
              disabled
              disabledColor="#fff"
              border="none"
              suffixIcon="arrow-right"
              :suffixIconStyle="{ fontSize: 24, color: '#555555' }">
            </u-input>
          </view>
        </view>
      </view>

      <view class="point-msg">
        <view class="pop-item flex align-center">
          <view class="pop-label"> 点位名称： </view>
          <view class="pop-content">
            {{ pointDetail.placeName || "-" }}
          </view>
        </view>
        <view class="pop-item flex align-center">
          <view class="pop-label"> 场景： </view>
          <view class="pop-content">
            {{ pointDetail.sceneNames || "-" }}
          </view>
        </view>
        <view class="pop-item flex align-center">
          <view class="pop-label"> 管理员： </view>
          <view class="pop-content">
            {{ pointDetail.adminName || "-" }}
          </view>
        </view>
        <view class="pop-item flex align-center">
          <view class="pop-label"> 区域/线路： </view>
          <view class="pop-content">
            {{ pointDetail.regionName || "-" }}
          </view>
        </view>
        <view class="pop-item flex align-center">
          <view class="pop-label"> 地址： </view>
          <view class="pop-content">
            {{ pointDetail.address || "-" }}
          </view>
        </view>
      </view>
    </view>
  </xpopup>
</template>

<script>
import { updateInfo } from "@/api/device/device.js";
import { placeDetail } from "@/api/point/point";

export default {
  name: "DeviceEditModal",
  props: {
    show: {
      type: Boolean,
      default: false
    },
    deviceId: {
      type: [String, Number],
      required: true
    },
    deviceDetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      editForm: {
        placeId: null,
        name: "",
        placeName: null,
      },
      pointDetail: {
        placeName: "",
        sceneNames: "",
        adminName: "",
        regionName: "",
        address: "",
      }
    };
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.initData();
      }
    }
  },
  mounted() {
    // 监听点位选择事件
    uni.$on("selectPoint", (res) => {
      this.choosePoint(res);
    });
  },
  beforeDestroy() {
    uni.$off("selectPoint");
  },
  methods: {
    initData() {
      // 数据反显
      this.editForm = {
        name: this.deviceDetail.deviceName
          ? this.deviceDetail.deviceName
          : this.deviceDetail.deviceId,
        placeId: this.deviceDetail.placeId,
        placeName: this.deviceDetail.placeName,
      };

      if (this.deviceDetail.placeId) {
        this.getPlaceDetail(this.deviceDetail.placeId);
      }
    },
    
    choosePoint(e) {
      this.pointDetail = e;
      this.editForm.name = this.deviceDetail.deviceName
        ? this.deviceDetail.deviceName
        : e.placeName; // 默认设置点位名称为设备名称
      this.editForm.placeName = e.placeName;
      this.editForm.placeId = e.id;
      this.$forceUpdate();
    },

    getPlaceDetail(id) {
      placeDetail({
        id: id,
      }).then((res) => {
        let data = res.data;
        this.pointDetail = data;
      });
    },

    handleClose() {
      this.$emit('close');
    },

    async handleConfirm() {
      if (!this.editForm.name) {
        this.$modal.showToast("设备名称必填！");
        return;
      }
      if (!this.editForm.placeId) {
        this.$modal.showToast("请先设置点位！");
        return;
      }
      
      try {
        await this.updateDevice();
        this.$emit('success');
        this.$emit('close');
      } catch (error) {
        console.error('更新设备失败:', error);
      }
    },

    // 修改设备信息
    updateDevice() {
      let params = {
        deviceId: Number(this.deviceId),
        deviceName: this.editForm.name,
        placeId: this.editForm.placeId,
      };
      return new Promise((resolve, reject) => {
        updateInfo(params)
          .then((res) => {
            this.$modal.msg("修改成功~");
            setTimeout(() => {
              resolve(res);
            }, 1000);
          })
          .catch((err) => {
            reject(err);
          });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.popup-content {
  padding: 0 24rpx;

  .point-name {
    padding: 0 20rpx;

    .pop-item {
      line-height: 66rpx;
      font-size: 28rpx;
      color: #555;

      .pop-label {
        width: 220rpx;
      }

      .pop-content {
        width: 480rpx;
      }
    }
  }

  .point-msg {
    background: #f6f7fa;
    border-radius: 14rpx;
    padding: 20rpx 36rpx;
    margin-top: 20rpx;

    .pop-item {
      line-height: 56rpx;
      font-size: 28rpx;
      color: #777777;

      .pop-label {
        width: 220rpx;
      }

      .pop-content {
        width: 480rpx;
      }
    }
  }
}
</style>
