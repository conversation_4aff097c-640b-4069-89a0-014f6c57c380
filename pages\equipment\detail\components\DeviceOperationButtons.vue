<template>
  <view class="btn-wrap" v-if="$parent.checkPermi(['devDetail:setting'])">
    <view
      class="grid"
      :class="[btnShowMore ? 'btn-box btn-box-more' : 'btn-box']">
      <view class="btn-item" v-if="$parent.checkPermi(['devDetail:reload'])">
        <xbutton
          bgColor="red"
          width="140rpx"
          @click="handleButtonClick('设备重启')"
          >设备重启</xbutton
        >
      </view>

      <view class="btn-item" v-if="$parent.checkPermi(['devDetail:voice'])">
        <xbutton
          bgColor="#fff"
          borderColor="#2C6FF3"
          color="#2C6FF3"
          width="140rpx"
          @click="handleButtonClick('声音设置')"
          >声音设置</xbutton
        >
      </view>

      <view
        class="btn-item"
        v-if="
          $parent.checkPermi(['devDetail:voice']) &&
          deviceDetail.deviceStatus &&
          deviceDetail.deviceStatus.tempValue != '无温控仪'
        ">
        <xbutton
          bgColor="#fff"
          borderColor="#2C6FF3"
          color="#2C6FF3"
          width="140rpx"
          @click="handleButtonClick('温度设置')"
          >温度设置</xbutton
        >
      </view>

      <view class="btn-item" v-if="$parent.checkPermi(['devDetail:light'])">
        <xbutton
          bgColor="#fff"
          borderColor="#2C6FF3"
          color="#2C6FF3"
          width="140rpx"
          @click="handleButtonClick('灯光设置')"
          >灯光设置</xbutton
        >
      </view>

      <view class="btn-item" v-if="$parent.checkPermi(['devDetail:adlight'])">
        <xbutton
          bgColor="#fff"
          borderColor="#2C6FF3"
          color="#2C6FF3"
          width="140rpx"
          @click="handleButtonClick('灯箱设置')"
          >灯箱设置</xbutton
        >
      </view>

      <view class="btn-item" v-if="$parent.checkPermi(['devDetail:clear'])">
        <xbutton
          bgColor="#fff"
          borderColor="#2C6FF3"
          color="#2C6FF3"
          width="140rpx"
          @click="handleButtonClick('清除故障')"
          >清除故障</xbutton
        >
      </view>

      <view class="btn-item" v-if="$parent.checkPermi(['devDetail:batch'])">
        <xbutton
          bgColor="#fff"
          borderColor="#2C6FF3"
          color="#2C6FF3"
          width="140rpx"
          @click="handleButtonClick('告警设置')"
          >告警设置</xbutton
        >
      </view>

      <view class="btn-item" v-if="$parent.checkPermi(['devDetail:blueTee'])">
        <xbutton
          bgColor="#fff"
          borderColor="#2C6FF3"
          color="#2C6FF3"
          width="140rpx"
          @click="handleButtonClick('蓝牙授权')"
          >蓝牙授权</xbutton
        >
      </view>

      <view class="btn-item" v-if="$parent.checkPermi(['devDetail:stop'])">
        <xbutton width="140rpx" @click="handleButtonClick(btnState)">{{
          btnState
        }}</xbutton>
      </view>

      <view
        class="btn-item"
        v-if="
          $parent.checkPermi(['devDetail:heaterStrip']) &&
          deviceDetail.deviceStatus &&
          deviceDetail.deviceStatus.tempValue != '无温控仪'
        ">
        <xbutton
          bgColor="#fff"
          borderColor="#2C6FF3"
          color="#2C6FF3"
          width="140rpx"
          @click="handleButtonClick('加热丝设置')"
          >加热丝设置</xbutton
        >
      </view>

      <view class="btn-item" v-if="$parent.checkPermi(['devDetail:openDoor'])">
        <xbutton
          bgColor="#fff"
          borderColor="#2C6FF3"
          color="#2C6FF3"
          width="140rpx"
          @click="handleButtonClick('开侧门')"
          >开侧门</xbutton
        >
      </view>

      <view
        class="btn-item"
        v-if="$parent.checkPermi(['devDetail:calibration'])">
        <xbutton
          bgColor="#fff"
          borderColor="#2C6FF3"
          color="#2C6FF3"
          width="140rpx"
          @click="handleButtonClick('电子秤校准')"
          >电子秤校准</xbutton
        >
      </view>

      <view class="btn-item" v-if="$parent.checkPermi(['devDetail:bandSn'])">
        <xbutton
          bgColor="#fff"
          borderColor="#2C6FF3"
          color="#2C6FF3"
          width="140rpx"
          @click="handleButtonClick('换绑SN')"
          >换绑SN</xbutton
        >
      </view>
    </view>

    <view
      class="btn-unfold flex align-center"
      @click="btnShowMore = !btnShowMore">
      <text>{{ btnShowMore ? "收起" : "更多设置" }}</text>
      <u-icon
        :name="btnShowMore ? 'arrow-up' : 'arrow-down'"
        color="#2C6FF3"
        size="16"></u-icon>
    </view>
  </view>
</template>

<script>
export default {
  name: "DeviceOperationButtons",
  props: {
    deviceDetail: {
      type: Object,
      default: () => ({}),
    },
    btnState: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      btnShowMore: false,
    };
  },
  methods: {
    handleButtonClick(buttonType) {
      this.$emit("buttonClick", buttonType);
    },
  },
};
</script>

<style lang="scss" scoped>
.btn-wrap {
  position: relative;
  padding-bottom: 60rpx;
  background-color: #fff;

  .btn-box {
    position: relative;
    height: 100rpx;
    transition: all 0.5s ease 0s;
    overflow: hidden;
    column-gap: 30rpx;

    &.btn-box-more {
      height: auto;
    }

    .btn-item {
      margin-top: 24rpx;
    }
  }

  .btn-unfold {
    height: 40rpx;
    position: absolute;
    right: 24rpx;
    bottom: 0;

    > text {
      line-height: 30rpx;
      color: #2c6ff3;
      font-size: 24rpx;
    }
  }
}
</style>
