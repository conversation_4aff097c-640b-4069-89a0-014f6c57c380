<template>
  <view class="xy-card">
    <view class="title"> 事件列表 </view>
    <scroll-view
      style="height: 410rpx"
      scroll-y
      lower-threshold="100"
      @scrolltolower="handleLoadMore">
      <view
        class="l-content"
        v-if="eventRecords && eventRecords.length > 0">
        <view class="l-item" v-for="item in eventRecords" :key="item.id">
          <view class="l-time">
            {{ item.createTime.substr(5) }}
          </view>
          <view class="l-status">
            {{ item.msg }}
          </view>
        </view>
      </view>
      <view class="l-content" v-else>
        <u-empty text="没有记录~"></u-empty>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { eventList } from "@/api/device/device.js"

export default {
  name: 'DeviceEventList',
  props: {
    deviceId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      eventRecords: [],
      eventPage: 1,
      loadmoreStatus: "loadmore"
    }
  },
  mounted() {
    this.getDeviceEventRecords()
  },
  methods: {
    // 获取设备事件列表
    getDeviceEventRecords() {
      eventList({
        deviceId: this.deviceId,
        page: {
          current: this.eventPage,
          size: 10,
        },
      }).then((res) => {
        let data = res.data.records
        this.loadmoreStatus = data.length < 10 ? "nomore" : "loadmore"
        this.eventRecords = this.eventRecords.concat(data)
      })
    },

    // 事件加载更多
    handleLoadMore() {
      if (this.loadmoreStatus === "nomore") return
      this.eventPage++
      this.getDeviceEventRecords()
    },

    // 刷新事件列表
    refreshEventList() {
      this.eventRecords = []
      this.eventPage = 1
      this.loadmoreStatus = "loadmore"
      this.getDeviceEventRecords()
    }
  }
}
</script>

<style lang="scss" scoped>
.title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  line-height: 54rpx;
}

.l-content {
  padding-top: 24rpx;

  .l-item {
    font-size: 28rpx;
    line-height: 50rpx;
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    + .l-item {
      margin-top: 24rpx;
    }

    .l-time {
      width: 252rpx;
    }

    .l-status {
      width: 540rpx;
    }
  }
}
</style>
