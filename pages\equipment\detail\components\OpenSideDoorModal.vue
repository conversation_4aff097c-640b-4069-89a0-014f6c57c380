<template>
  <xpopup
    :show="show"
    @close="handleClose"
    @confirm="handleConfirm"
    :showBtn="true"
    title="开侧门">
    <view class="popup-content restart"> 是否确定开侧门? </view>
  </xpopup>
</template>

<script>
import { sendCommand } from "@/api/device/device.js";
import getDict from "@/utils/getDict.js";

export default {
  name: "OpenSideDoorModal",
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    deviceId: {
      type: [String, Number],
      required: true,
    },
  },
  methods: {
    getDict,

    handleClose() {
      this.$emit("close");
    },

    async handleConfirm() {
      try {
        let templet = null;
        await this.getDict("mqtt_cmd_templet_task", "locker").then((res) => {
          templet = JSON.parse(res[0].value);
          templet.data.task = "open";
          templet.data.jgh = "1";
          templet.data.id = "0";
          templet.data.appId = undefined;
          templet.data.src = undefined;
        });

        await sendCommand([
          {
            deviceId: this.deviceId,
            templet: templet,
          },
        ]);

        this.$modal.showToast("开侧门成功~");
        this.$emit("success");
        this.$emit("close");
      } catch (error) {
        console.error("开侧门失败:", error);
        this.$modal.showToast("开侧门失败，请重试");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.popup-content {
  padding: 0 24rpx;
}

.restart {
  padding: 40rpx 0;
  font-size: 34rpx;
  text-align: center;
}
</style>
