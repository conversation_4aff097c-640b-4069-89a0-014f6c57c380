<template>
  <xpopup
    :show="show"
    @close="handleClose"
    @confirm="handleConfirm"
    :showBtn="true"
    title="换绑SN">
    <view class="popup-content blueTe">
      <view class="info">
        <view>更换设备的SN号码，请谨慎操作！</view>
      </view>
      <view class="blueTe-msg">
        <view>当前SN：</view>
        <view>
          {{ deviceDetail.deviceSysinfo.deviceSn || "-" }}
        </view>
      </view>
      <view class="blueTe-msg">
        <view>新SN码：</view>
        <view>
          <u--input
            placeholder="请输入新的SN码"
            border="surround"
            v-model="snCode">
          </u--input>
        </view>
      </view>
    </view>
  </xpopup>
</template>

<script>
import { updateSn } from "@/api/device/device.js";

export default {
  name: "SnBindingModal",
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    deviceId: {
      type: [String, Number],
      required: true,
    },
    deviceDetail: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      snCode: "",
    };
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.snCode = "";
      }
    },
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },

    async handleConfirm() {
      if (!this.snCode) {
        this.$modal.msg("请输入新的SN码！");
        return;
      }

      try {
        await updateSn({
          deviceId: this.deviceId,
          deviceSn: this.snCode,
        });

        this.$modal.showToast("SN换绑成功！");
        this.$emit("success");
        this.$emit("close");
      } catch (error) {
        console.error("SN换绑失败:", error);
        this.$modal.showToast("SN换绑失败，请重试");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.popup-content {
  padding: 0 24rpx;
}

.blueTe {
  .info {
    color: red;
    line-height: 46rpx;
  }

  .blueTe-msg {
    display: flex;
    flex-flow: row nowrap;
    justify-content: flex-start;
    align-items: center;
    margin-top: 24rpx;

    > view:nth-child(1) {
      width: 150rpx;
    }

    > view:nth-child(2) {
      width: 600rpx;
    }
  }
}
</style>
