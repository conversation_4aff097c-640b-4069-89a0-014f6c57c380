<template>
  <xpopup
    :show="show"
    @close="handleClose"
    @confirm="handleConfirm"
    :showBtn="true"
    title="蓝牙授权">
    <view class="popup-content blueTe">
      <view class="info">
        <view>1）在蓝牙软件上输入授权码后可以通过蓝牙控制机器。</view>
        <view>
          2）授权码只能由机器管理员生成,必须在30分钟内使用,且只能使用一次。
        </view>
      </view>
      <view class="blueTe-msg">
        <view>机器编号：</view>
        <view>
          {{ deviceDetail.deviceId }}
        </view>
      </view>
      <view class="blueTe-msg">
        <view>授权码：</view>
        <view>
          <u--input
            placeholder="请输入内容"
            border="surround"
            v-model="blueCode">
          </u--input>
        </view>
      </view>
    </view>
  </xpopup>
</template>

<script>
import { genCode } from "@/api/device/device.js";

export default {
  name: "BluetoothAuthModal",
  props: {
    show: {
      type: Boolean,
      default: false
    },
    deviceDetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      blueCode: ""
    };
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.blueCode = "";
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('close');
    },

    async handleConfirm() {
      if (!this.blueCode) {
        this.$modal.msg("请输入授权码！");
        return;
      }
      
      try {
        await genCode({
          authCode: this.blueCode,
          deviceSn: this.deviceDetail.deviceSysinfo.deviceSn,
        });
        
        this.$modal.showToast("授权成功,请尽快使用！");
        this.$emit('success');
        this.$emit('close');
      } catch (error) {
        console.error('蓝牙授权失败:', error);
        this.$modal.showToast("授权失败，请重试");
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.popup-content {
  padding: 0 24rpx;
}

.blueTe {
  .info {
    color: red;
    line-height: 46rpx;
  }

  .blueTe-msg {
    display: flex;
    flex-flow: row nowrap;
    justify-content: flex-start;
    align-items: center;
    margin-top: 24rpx;

    > view:nth-child(1) {
      width: 150rpx;
    }

    > view:nth-child(2) {
      width: 600rpx;
    }
  }
}
</style>
