<template>
  <xpopup
    :show="show"
    @close="handleClose"
    @confirm="handleConfirm"
    :showBtn="true"
    title="温度设置">
    <view class="popup-content temp">
      <view class="flex" style="margin-top: 10rpx">
        <view class="lab" style="margin-top: -10rpx">工作模式:</view>
        <u-radio-group v-model="tempDetail.selTempWorkModel" placement="row">
          <u-radio
            :customStyle="{ marginBottom: '16rpx', marginLeft: '16rpx' }"
            v-for="(item, index) in tempWorkModels"
            :key="index"
            :label="item.name"
            :name="item.id">
          </u-radio>
        </u-radio-group>
      </view>

      <view class="flex" style="margin-top: 20rpx">
        <view class="lab" style="margin-top: -10rpx">温度分配:</view>
        <u-radio-group v-model="tempType" placement="row">
          <u-radio
            :customStyle="{ marginBottom: '16rpx', marginLeft: '16rpx' }"
            v-for="(item, index) in tempTypeModels"
            :key="index"
            :label="item.name"
            :name="item.id">
          </u-radio>
        </u-radio-group>
      </view>

      <view class="tips">
        提示：请根据需求设置温度，饮料零食等商品建议温度设置范围 4~10℃
      </view>

      <view
        class="flex align-center"
        style="margin-top: 30rpx; margin-bottom: 20rpx"
        v-if="tempType == '0'">
        <view class="lab" style="margin-top: 0">工作温度(℃):</view>
        <view style="margin-left: 20rpx">
          <u--input v-model="tempDetail.target"></u--input>
        </view>
      </view>

      <view v-else>
        <view class="tips">
          提示：时段设置为X点至Y点，结束时间需大于起始时间
        </view>
        <!-- 时段设置部分 -->
        <view class="flex align-center">
          <view class="lab">时段1:</view>
          <view
            class="flex align-center date-container marleft"
            style="padding-left: 10rpx; padding-right: 10rpx"
            @tap="pickerTimes(0)">
            <view class="time-box">
              {{ tempDetail.start1 }}
            </view>
            <view class="lab to">至</view>
            <view class="time-box">{{ tempDetail.end1 }}</view>
          </view>
          <view class="flex align-center temp-item justify-between">
            <view> 温度： </view>
            <view>
              <u--input
                inputAlign="center"
                v-model="tempDetail.target1"
                :customStyle="{
                  width: '100rpx',
                  height: '50rpx',
                }">
              </u--input>
            </view>
            <view>℃</view>
          </view>
        </view>

        <view class="flex align-center">
          <view class="lab">时段2:</view>
          <view
            class="flex align-center date-container marleft"
            style="padding-left: 10rpx; padding-right: 10rpx"
            @tap="pickerTimes(1)">
            <view class="time-box">
              {{ tempDetail.start2 }}
            </view>
            <view class="lab to">至</view>
            <view class="time-box">{{ tempDetail.end2 }}</view>
          </view>
          <view class="flex align-center temp-item justify-between">
            <view> 温度： </view>
            <view>
              <u--input
                inputAlign="center"
                v-model="tempDetail.target2"
                :customStyle="{
                  width: '100rpx',
                  height: '50rpx',
                }">
              </u--input>
            </view>
            <view>℃</view>
          </view>
        </view>

        <view class="flex align-center">
          <view class="lab">时段3:</view>
          <view
            class="flex align-center date-container marleft"
            style="padding-left: 10rpx; padding-right: 10rpx"
            @tap="pickerTimes(2)">
            <view class="time-box">
              {{ tempDetail.start3 }}
            </view>
            <view class="lab to">至</view>
            <view class="time-box">{{ tempDetail.end3 }}</view>
          </view>
          <view class="flex align-center temp-item justify-between">
            <view> 温度： </view>
            <view>
              <u--input
                inputAlign="center"
                v-model="tempDetail.target3"
                :customStyle="{
                  width: '100rpx',
                  height: '50rpx',
                }">
              </u--input>
            </view>
            <view>℃</view>
          </view>
        </view>
      </view>
      <view style="height: 20rpx"></view>
    </view>

    <!-- 时间选择器 -->
    <u-picker
      :show="showTimeSelPicker"
      ref="uPicker"
      :columns="times"
      @confirm="pickerTimeConfirm"
      closeOnClickOverlay
      @close="showTimeSelPicker = false"
      @cancel="showTimeSelPicker = false">
    </u-picker>
  </xpopup>
</template>

<script>
import { sendCommand, getLastTem } from "@/api/device/device.js";
import getDict from "@/utils/getDict.js";

export default {
  name: "TemperatureSettingModal",
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    deviceId: {
      type: [String, Number],
      required: true,
    },
  },
  data() {
    return {
      tempDetail: {
        target: "",
        selTempWorkModel: 2,
        start1: 0,
        end1: 0,
        target1: 4,
        start2: 0,
        end2: 0,
        target2: 4,
        start3: 0,
        end3: 0,
        target3: 4,
        curTimes: 0,
      },
      tempWorkModels: [
        { name: "制热", id: 1 },
        { name: "制冷", id: 2 },
        { name: "恒温", id: 3 },
        { name: "关闭", id: 4 },
      ],
      tempTypeModels: [
        { name: "全天", id: 0 },
        { name: "时段", id: 1 },
      ],
      tempType: 0,
      showTimeSelPicker: false,
      times: [
        [
          0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19,
          20, 21, 22, 23, 24,
        ],
        [
          0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19,
          20, 21, 22, 23, 24,
        ],
      ],
    };
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.loadTemperatureSettings();
      }
    },
  },
  methods: {
    getDict,

    handleClose() {
      this.$emit("close");
    },

    async handleConfirm() {
      try {
        if (this.validateParams()) return;

        let templet = null;
        await this.getDict("mqtt_cmd_templet_sets", "temperature").then(
          (res) => {
            templet = JSON.parse(res[0].value);
            if (this.tempType == 0) {
              templet.data.workModel = this.tempDetail.selTempWorkModel;
              templet.data.target = this.tempDetail.target;
              templet.data.start1 = null;
              templet.data.end1 = null;
              templet.data.target1 = null;
              templet.data.start2 = null;
              templet.data.end2 = null;
              templet.data.target2 = null;
              templet.data.start3 = null;
              templet.data.end3 = null;
              templet.data.target3 = null;
            } else {
              templet.data.workModel = this.tempDetail.selTempWorkModel;
              templet.data.target = this.tempDetail.target1;
              templet.data.start1 = this.tempDetail.start1;
              templet.data.end1 = this.tempDetail.end1;
              templet.data.target1 = this.tempDetail.target1;
              templet.data.start2 = this.tempDetail.start2;
              templet.data.end2 = this.tempDetail.end2;
              templet.data.target2 = this.tempDetail.target2;
              templet.data.start3 = this.tempDetail.start3;
              templet.data.end3 = this.tempDetail.end3;
              templet.data.target3 = this.tempDetail.target3;
            }
          }
        );

        await sendCommand([
          {
            deviceId: this.deviceId,
            templet: templet,
          },
        ]);

        this.$modal.showToast("设置成功~");
        this.$emit("success");
        this.$emit("close");
      } catch (error) {
        console.error("温度设置失败:", error);
        this.$modal.showToast("设置失败，请重试");
      }
    },

    loadTemperatureSettings() {
      getLastTem({
        deviceId: this.deviceId,
      })
        .then((res) => {
          let data = res.data;
          this.tempType = data && data.tempValue1 ? 1 : 0;
          if (data) {
            let tempData = {
              selTempWorkModel: Number(data.workMode),
              target: data.tempValue != null ? data.tempValue : "",
              start1: data.job1StartTime != null ? data.job1StartTime : "",
              end1: data.job1EndTime != null ? data.job1EndTime : "",
              target1: data.tempValue1 != null ? data.tempValue1 : "",
              start2: data.job2StartTime != null ? data.job2StartTime : "",
              end2: data.job2EndTime != null ? data.job2EndTime : "",
              target2: data.tempValue2 != null ? data.tempValue2 : "",
              start3: data.job3StartTime != null ? data.job3StartTime : "",
              end3: data.job3EndTime != null ? data.job3EndTime : "",
              target3: data.tempValue3 != null ? data.tempValue3 : "",
              curTimes: data.tempValue,
            };
            this.tempDetail = JSON.parse(JSON.stringify(tempData));
          }
        })
        .catch(() => {
          // 加载失败时使用默认值
        });
    },

    validateParams() {
      if (this.tempType == 1) {
        if (
          this.tempDetail.start1 > this.tempDetail.end1 ||
          this.tempDetail.start2 > this.tempDetail.end2 ||
          this.tempDetail.start3 > this.tempDetail.end3
        ) {
          this.$modal.msg("结束时间需大于等于起始时间！");
          return true;
        }
      }
      return false;
    },

    pickerTimes(index) {
      this.tempDetail.curTimes = index;
      this.showTimeSelPicker = true;
    },

    pickerTimeConfirm(e) {
      this.showTimeSelPicker = false;
      const index = this.tempDetail.curTimes;
      const fieldMap = {
        0: ["start1", "end1"],
        1: ["start2", "end2"],
        2: ["start3", "end3"],
      };

      if (fieldMap[index]) {
        const [startField, endField] = fieldMap[index];
        this.tempDetail[startField] = e.value[0];
        this.tempDetail[endField] = e.value[1];
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.popup-content {
  padding: 0 24rpx;
}

.temp {
  .tips {
    color: red;
    line-height: 46rpx;
    margin-top: 20rpx;
  }

  .flex {
    margin-top: 20rpx;
    height: 60rpx;

    .lab {
      line-height: 60rpx;
      height: 60rpx;
      text-align: center;
      font-weight: 600;
      margin-top: 20rpx;
    }

    .to {
      width: 60rpx;
      margin-left: 20rpx;
      margin-top: 0;
      font-weight: 500;
    }

    .time-box {
      height: 50rpx;
      width: 100rpx;
      line-height: 50rpx;
      margin-left: 12rpx;
      text-align: center;
      border-radius: 10rpx;
      border-style: solid;
      border-width: 1px;
      border-color: #dadbde;
    }
  }
}

.temp-item {
  width: 230rpx;
  margin-left: 24rpx;

  > view:nth-child(1) {
    font-weight: 600;
  }

  > view:nth-child(2) {
    width: 100rpx;
    height: 50rpx;
    border-radius: 10rpx;
  }
}
</style>
