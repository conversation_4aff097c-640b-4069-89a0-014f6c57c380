<template>
  <xpopup
    :show="show"
    @close="handleClose"
    @confirm="handleConfirm"
    :showBtn="true"
    title="灯光设置">
    <view class="popup-content temp">
      <view
        class="flex align-center justify-between"
        style="margin-bottom: 24rpx">
        <view class="lab" style="margin: 0 24rpx 0 0">是否打开:</view>
        <u-switch v-model="lightDetail.lightControl"></u-switch>
      </view>

      <view class="info">时段设置为X点至Y点，结束时间需大于起始时间</view>
      <view class="flex align-center">
        <view class="lab">时段1:</view>
        <view
          class="flex align-center date-container marleft"
          style="padding-left: 10rpx; padding-right: 10rpx"
          @tap="pickerTimes(0)">
          <view class="time-box-l">
            {{ lightDetail.start1 }}
          </view>
          <view class="lab to">至</view>
          <view class="time-box-l">{{ lightDetail.end1 }}</view>
        </view>
      </view>
      <view class="flex align-center">
        <view class="lab">时段2:</view>
        <view
          class="flex align-center date-container marleft"
          style="padding-left: 10rpx; padding-right: 10rpx"
          @tap="pickerTimes(1)">
          <view class="time-box-l">
            {{ lightDetail.start2 }}
          </view>
          <view class="lab to">至</view>
          <view class="time-box-l">{{ lightDetail.end2 }}</view>
        </view>
      </view>
      <view class="flex align-center">
        <view class="lab">时段3:</view>
        <view
          class="flex align-center date-container marleft"
          style="padding-left: 10rpx; padding-right: 10rpx"
          @tap="pickerTimes(2)">
          <view class="time-box-l">
            {{ lightDetail.start3 }}
          </view>
          <view class="lab to">至</view>
          <view class="time-box-l">{{ lightDetail.end3 }}</view>
        </view>
      </view>
      <view style="height: 20rpx"></view>
    </view>

    <!-- 时间选择器 -->
    <u-picker
      :show="showTimeSelPicker"
      ref="uPicker"
      :columns="times"
      @confirm="pickerTimeConfirm"
      closeOnClickOverlay
      @close="showTimeSelPicker = false"
      @cancel="showTimeSelPicker = false">
    </u-picker>
  </xpopup>
</template>

<script>
import { sendCommand, lastOne } from "@/api/device/device.js";
import getDict from "@/utils/getDict.js";

export default {
  name: "LightSettingModal",
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    deviceId: {
      type: [String, Number],
      required: true,
    },
  },
  data() {
    return {
      lightDetail: {
        lightControl: true,
        start1: 0,
        end1: 0,
        start2: 0,
        end2: 0,
        start3: 0,
        end3: 0,
        curTimes: 0,
      },
      showTimeSelPicker: false,
      times: [
        [
          0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19,
          20, 21, 22, 23, 24,
        ],
        [
          0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19,
          20, 21, 22, 23, 24,
        ],
      ],
    };
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.loadLightSettings();
      }
    },
  },
  methods: {
    getDict,

    handleClose() {
      this.$emit("close");
    },

    async handleConfirm() {
      try {
        let templet = null;
        await this.getDict("mqtt_cmd_templet_sets", "light").then((res) => {
          templet = JSON.parse(res[0].value);
          templet.data.lightControl = this.lightDetail.lightControl;
          templet.data.light0.start1 = this.lightDetail.start1;
          templet.data.light0.end1 = this.lightDetail.end1;
          templet.data.light0.start2 = this.lightDetail.start2;
          templet.data.light0.end2 = this.lightDetail.end2;
          templet.data.light0.start3 = this.lightDetail.start3;
          templet.data.light0.end3 = this.lightDetail.end3;
        });

        await sendCommand([
          {
            deviceId: this.deviceId,
            templet: templet,
          },
        ]);

        this.$modal.showToast("设置成功~");
        this.$emit("success");
        this.$emit("close");
      } catch (error) {
        console.error("灯光设置失败:", error);
        this.$modal.showToast("设置失败，请重试");
      }
    },

    loadLightSettings() {
      lastOne({
        deviceId: this.deviceId,
        typeList: ["light"],
      })
        .then((res) => {
          if (res.data[0] && res.data[0].content) {
            let data = JSON.parse(res.data[0].content);
            this.lightDetail = {
              lightControl: data.lightControl,
              start1: data.light0.start1,
              end1: data.light0.end1,
              start2: data.light0.start2,
              end2: data.light0.end2,
              start3: data.light0.start3,
              end3: data.light0.end3,
            };
          }
        })
        .catch(() => {
          // 加载失败时使用默认值
        });
    },

    pickerTimes(index) {
      this.lightDetail.curTimes = index;
      this.showTimeSelPicker = true;
    },

    pickerTimeConfirm(e) {
      this.showTimeSelPicker = false;
      const index = this.lightDetail.curTimes;
      const fieldMap = {
        0: ["start1", "end1"],
        1: ["start2", "end2"],
        2: ["start3", "end3"],
      };

      if (fieldMap[index]) {
        const [startField, endField] = fieldMap[index];
        this.lightDetail[startField] = e.value[0];
        this.lightDetail[endField] = e.value[1];
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.popup-content {
  padding: 0 24rpx;
}

.temp {
  .info {
    color: red;
    height: 60rpx;
    line-height: 60rpx;
    text-align: center;
  }

  .flex {
    margin-top: 20rpx;
    height: 60rpx;

    .lab {
      line-height: 60rpx;
      height: 60rpx;
      text-align: center;
      font-weight: 600;
      margin-top: 20rpx;
    }

    .to {
      width: 60rpx;
      margin-left: 20rpx;
      margin-top: 0;
      font-weight: 500;
    }

    .time-box-l {
      height: 50rpx;
      width: 230rpx;
      line-height: 50rpx;
      margin-left: 12rpx;
      text-align: center;
      border-radius: 10rpx;
      border-style: solid;
      border-width: 1px;
      border-color: #dadbde;
    }
  }
}
</style>
