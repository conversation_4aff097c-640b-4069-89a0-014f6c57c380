<template>
  <view>
    <!-- 图表选项卡 -->
    <view
      class="xy-card"
      style="
        margin-bottom: 0;
        border-bottom: 1rpx solid #f1f1f1;
        padding: 12rpx 0 24rpx;
      ">
      <u-tabs
        :list="chartTab"
        :scrollable="false"
        @click="handleTabClick"
        lineColor="#2C6FF3"></u-tabs>
    </view>

    <!-- 图表内容 -->
    <view class="xy-card">
      <view class="chart-content">
        <!-- 经营数据 -->
        <view class="total" v-if="current == '经营数据'">
          <view style="width: 200rpx">
            <u-subsection
              activeColor="#2C6FF3"
              :list="totalTab"
              :current="time"
              @change="handleTotalChange">
            </u-subsection>
          </view>
          <view class="t-content">
            <view class="t-item">
              <view class="t-name"> 销售额 </view>
              <view class="t-num">
                ￥<text>{{ $xy.delMoney(countData.salesMoney) }}</text>
              </view>
            </view>
            <view class="t-item">
              <view class="t-name"> 订单数 </view>
              <view class="t-num">
                <text>{{ countData.salesCount || 0 }}</text>
              </view>
            </view>
            <view class="t-item">
              <view class="t-name"> 退款 </view>
              <view class="t-num">
                ￥<text>{{ $xy.delMoney(countData.refundMoney) }}</text>
              </view>
            </view>
          </view>

          <view class="more-data">
            <xbutton
              width="140"
              @click="handleOrderDetails"
              v-if="$parent.checkPermi(['devDetail:orderList'])">
              交易明细
            </xbutton>
            <xbutton
              style="margin-left: 20rpx"
              width="140"
              @click="handleMoreData"
              v-if="$parent.checkPermi(['devDetail:salesData'])">
              销售统计
            </xbutton>
          </view>
        </view>

        <!-- 温度图表 -->
        <temperature-chart
          v-else-if="current == '温度图表'"
          :deviceDetail="deviceDetail"
          :chartData="tempChartData">
        </temperature-chart>
      </view>
    </view>

    <!-- 商品管理 -->
    <view class="xy-card">
      <view class="title"> 商品管理 </view>
      <view class="t-content">
        <view class="t-item">
          <view class="t-name"> 在售商品种类 </view>
          <view class="t-num">
            <text>{{ goodsManageData.categoryNum || 0 }}</text
            >种
          </view>
        </view>
        <view class="t-item">
          <view class="t-name"> 在售库存 </view>
          <view class="t-num">
            <text>{{ goodsManageData.stock || 0 }}</text>
          </view>
        </view>
        <view class="t-item">
          <view class="t-name"> 上次补货后库存 </view>
          <view class="t-num">
            <text>{{ goodsManageData.afterFillStock || 0 }}</text>
          </view>
        </view>
      </view>
      <view class="bot bot1">
        <view v-if="$parent.checkPermi(['devDetail:devComList'])">
          <xbutton
            bgColor="#fff"
            borderColor="#2C6FF3"
            color="#2C6FF3"
            width="140rpx"
            @click="handleManageGoods">
            管理商品</xbutton
          >
        </view>
        <view v-if="$parent.checkPermi(['devDetail:supplyRec'])">
          <xbutton
            bgColor="#fff"
            borderColor="#2C6FF3"
            color="#2C6FF3"
            width="140rpx"
            @click="handleSupplyRecord">
            补货记录</xbutton
          >
        </view>
        <view v-if="$parent.checkPermi(['devDetail:invRec'])">
          <xbutton
            bgColor="#fff"
            borderColor="#2C6FF3"
            color="#2C6FF3"
            width="140rpx"
            @click="handleInventoryRecord">
            盘点记录</xbutton
          >
        </view>
        <view v-if="$parent.checkPermi(['devDetail:rep'])">
          <xbutton width="140rpx" @click="handleReplenish"> 补货</xbutton>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import TemperatureChart from "./TemperatureChart.vue";

export default {
  name: "DeviceStatistics",
  components: {
    TemperatureChart,
  },
  props: {
    deviceDetail: {
      type: Object,
      default: () => ({}),
    },
    deviceId: {
      type: String,
      default: "",
    },
    allCountData: {
      type: Object,
      default: () => ({}),
    },
    tempChartData: {
      type: Object,
      default: () => ({
        series: [],
        categories: [],
      }),
    },
    goodsManageData: {
      type: Object,
      default: () => ({
        categoryNum: 0,
        stock: 0,
        afterFillStock: 0,
      }),
    },
  },
  data() {
    return {
      current: "经营数据",
      chartTab: [
        {
          name: "经营数据",
        },
        {
          name: "温度图表",
        },
      ],
      totalTab: ["今日", "本月"],
      time: 0,
      countData: {
        salesMoney: 0,
        salesCount: 0,
        refundMoney: 0,
      },
    };
  },
  watch: {
    allCountData: {
      handler(newVal) {
        if (newVal && newVal.dayBusinessData) {
          this.countData =
            this.time === 0 ? newVal.dayBusinessData : newVal.monthBusinessData;
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    handleTabClick(e) {
      this.current = e.name;
      this.$emit("tabChange", e.name);
    },

    handleTotalChange(e) {
      this.time = e;
      if (this.time == 0) {
        // 今日
        this.countData = this.allCountData.dayBusinessData || {};
      } else {
        // 今月
        this.countData = this.allCountData.monthBusinessData || {};
      }
    },

    handleOrderDetails() {
      let deviceName = this.deviceDetail.deviceName;
      this.$tab.navigateTo(
        `/pages/order/deviceOrderList?name=${deviceName}&id=${this.deviceDetail.deviceId}`
      );
    },

    handleMoreData() {
      let deviceName = this.deviceDetail.deviceName
        ? this.deviceDetail.deviceName
        : this.deviceDetail.deviceId;
      this.$tab.navigateTo(
        `/pages/equipment/statistics?title=${deviceName}&id=${this.deviceId}`
      );
    },

    handleManageGoods() {
      this.$tab.navigateTo(
        `/pages/equipment/comManage?id=${this.deviceId}&deviceName=${this.deviceDetail.deviceName}`
      );
    },

    handleSupplyRecord() {
      this.$tab.navigateTo(
        `/pages/replenish/repRecord?deviceId=${this.deviceId}`
      );
    },

    handleInventoryRecord() {
      this.$tab.navigateTo(
        `/pages/replenish/physicalRecord?deviceId=${this.deviceId}`
      );
    },

    handleReplenish() {
      this.$emit("replenish");
    },
  },
};
</script>

<style lang="scss" scoped>
.chart-content {
  width: 100%;

  .total {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .more-data {
    margin-top: 24rpx;
  }
}

.t-content {
  display: flex;
  flex-flow: row nowrap;
  width: 100%;
  box-sizing: border-box;
  text-align: center;

  .t-item {
    width: 50%;
    margin-top: 24rpx;

    .t-name {
      font-size: 28rpx;
      line-height: 40rpx;
    }

    .t-num {
      font-size: 28rpx;
      line-height: 46rpx;
      padding: 12rpx 0;

      text {
        font-size: 40rpx;
        font-weight: bold;
      }
    }
  }
}

.title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  line-height: 54rpx;
}

.bot1 {
  display: flex;
  justify-content: flex-end;

  > view {
    margin-left: 24rpx;
  }
}
</style>
