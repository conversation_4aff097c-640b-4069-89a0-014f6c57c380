<template>
  <view class="xy-card">
    <view class="top">
      <view>
        <view class="t-left">
          <view v-if="deviceDetail.deviceName"
            >{{ deviceDetail.deviceName
            }}<text style="color: #666; font-size: 26rpx"
              >({{ deviceDetail.deviceId }})</text
            ></view
          >
          <view v-else>{{ deviceDetail.deviceId }}</view>
        </view>

        <view class="flex" style="white-space: nowrap">
          <!-- 断电/在线/离线 -->
          <view
            :class="[deviceDetail.sysPower == 2 ? 't-right off' : 't-right']"
            v-if="deviceDetail.sysPower == 2">
            已断电
          </view>
          <view
            :class="[
              deviceDetail.netStateName == '在线' ? 't-right' : 't-right off',
            ]"
            v-else>
            {{ deviceDetail.netStateName || "离线" }}
          </view>
          <view
            :class="[deviceDetail.busyState == 1 ? 't-right' : 't-right off']">
            {{ deviceDetail.busyState == 1 ? "运营中" : "已停运" }}
          </view>
        </view>
      </view>
      <view class="flex flex-between" style="margin-top: 24rpx">
        <view v-show="deviceDetail.deviceType != '5'">
          <xbutton
            v-show="activityState.length > 0"
            round="25rpx"
            padding="4rpx 10rpx"
            size="mini"
            style="margin-right: 12rpx"
            :bgColor="activityState == '异常' ? '#ff0000' : '#66CC00'"
            color="#fff"
            >{{ activityState }}</xbutton
          >
          <xbutton
            round="25rpx"
            padding="0 10rpx"
            size="mini"
            style="margin-right: 12rpx"
            :bgColor="
              deviceDetail.deviceStatus.doorStateL == 2 ? '#66CC00' : '#FFCC33'
            "
            color="#fff">
            {{
              deviceDetail.deviceStatus.doorStateL == 2 ? "已关门" : "已开门"
            }}
          </xbutton>
        </view>

        <view class="flex flex-between">
          <qr-code-downloader
            v-if="$parent.checkPermi(['devDetail:downQr'])"
            :deviceId="deviceId"></qr-code-downloader>
          <xbutton
            size="mini"
            @click="handleEdit"
            v-if="$parent.checkPermi(['devDetail:edit'])"
            >编辑
          </xbutton>
        </view>
      </view>
    </view>

    <view :class="[isMore ? 'center center-more' : 'center']">
      <view class="d-line">
        <view class="c-item">
          <view class="name"> 编号： </view>
          <view class="val">
            {{ deviceDetail.deviceId || "-" }}
            <text
              @click="copy(deviceDetail.deviceId)"
              v-if="deviceDetail.mercDeviceCode"
              >复制</text
            >
          </view>
        </view>
        <view class="c-item">
          <view class="name"> 商户： </view>
          <view class="val">
            {{ deviceDetail.mercName || "-" }}
          </view>
        </view>
      </view>
      <view class="d-line">
        <view class="c-item">
          <view class="name"> 温度： </view>
          <view class="val">
            {{ deviceDetail.deviceStatus.tempValue || "-" }}
          </view>
        </view>
        <view class="c-item" style="align-items: center">
          <view class="name"> 强度： </view>
          <view class="val net flex align-end">
            <view
              :class="[
                deviceDetail.deviceStatus.netDbm > -100
                  ? 'dbm1 dbm-green'
                  : 'dbm1',
              ]"></view>
            <view
              :class="[
                deviceDetail.deviceStatus.netDbm > -88
                  ? 'dbm2 dbm-green'
                  : 'dbm2',
              ]"></view>
            <view
              :class="[
                deviceDetail.deviceStatus.netDbm > -78
                  ? 'dbm3 dbm-green'
                  : 'dbm3',
              ]"></view>
            <view
              :class="[
                deviceDetail.deviceStatus.netDbm > -55
                  ? 'dbm4 dbm-green'
                  : 'dbm4',
              ]"></view>
          </view>
        </view>
      </view>

      <view class="c-item">
        <view class="name"> 设备位置： </view>
        <view class="val" @click="showPos" style="width: 120rpx">
          <u-icon slot="right" size="20" name="map"></u-icon>
        </view>
      </view>

      <view class="c-item">
        <view class="name"> 流量卡号： </view>
        <view class="val">
          {{ deviceDetail.deviceSysinfo.simIccid || "-"
          }}<text
            v-if="deviceDetail.deviceSysinfo.simIccid"
            @click="copy(deviceDetail.deviceSysinfo.simIccid)">
            复制</text
          >
        </view>
      </view>

      <view class="c-item">
        <view class="name"> 流量卡运营商： </view>
        <view class="val">
          {{ deviceDetail.deviceSysinfo.simIsp || "-" }}
        </view>
      </view>

      <view class="c-item">
        <view class="name"> 软件版本： </view>
        <view class="val">
          {{ deviceDetail.deviceSysinfo.appUpmVersion || "-" }}
          <text
            v-if="
              newVersion &&
              newVersion !== deviceDetail.deviceSysinfo.appUpmVersion
            "
            @click="handleUpgrade">
            立刻升级
          </text>
        </view>
      </view>
      <view class="c-item">
        <view class="name"> 最后更新时间： </view>
        <view class="val">
          {{ deviceDetail.deviceStatus.updateTime || "-" }}
        </view>
      </view>
      <view class="c-item">
        <view class="name"> 激活状态： </view>
        <view class="val" v-if="deviceDetail.activeState == 1"> 已激活 </view>
        <view class="val" v-else> 未激活 </view>
      </view>
      <view class="c-item">
        <view class="name"> 激活时间： </view>
        <view class="val">
          {{ deviceDetail.activeTime || "-" }}
        </view>
      </view>
      <view class="c-item">
        <view class="name"> 区域： </view>
        <view class="val">
          {{ deviceDetail.districtName || "-" }}
        </view>
      </view>
      <view class="c-item">
        <view class="name"> 点位： </view>
        <view class="val">
          {{ deviceDetail.placeName || "-" }}
        </view>
      </view>
      <view class="unfold flex align-center" @click="isMore = !isMore">
        <text>{{ isMore ? "更多信息" : "收起" }}</text>
        <u-icon
          :name="isMore ? 'arrow-down' : 'arrow-up'"
          color="#2C6FF3"
          size="16"></u-icon>
      </view>
    </view>
  </view>
</template>

<script>
import QrCodeDownloader from "./QrCodeDownloader.vue";

export default {
  name: "DeviceInfoDisplay",
  components: {
    QrCodeDownloader,
  },
  props: {
    deviceDetail: {
      type: Object,
      default: () => ({}),
    },
    deviceId: {
      type: String,
      default: "",
    },
    activityState: {
      type: String,
      default: "",
    },
    newVersion: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      isMore: true,
    };
  },
  methods: {
    // 复制文字
    copy(text) {
      uni.setClipboardData({
        data: text,
      });
    },

    // 显示位置
    showPos() {
      if (this.deviceDetail.lat && this.deviceDetail.lon) {
        uni.openLocation({
          latitude: Number(this.deviceDetail.lat),
          longitude: Number(this.deviceDetail.lon),
          name: this.deviceDetail.deviceName || this.deviceDetail.deviceId,
          address: this.deviceDetail.placeName || "-",
          success: () => {
            getApp().globalData.isOnShow = false;
          },
        });
      } else {
        this.$modal.msg("请先编辑设备位置信息~");
      }
    },

    // 编辑设备
    handleEdit() {
      this.$emit("edit");
    },

    // 升级处理
    handleUpgrade() {
      this.$emit("upgrade");
    },
  },
};
</script>

<style lang="scss" scoped>
.top {
  > view:nth-child(1) {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;

    .t-left {
      width: 380rpx;

      > view:nth-child(1) {
        font-size: 36rpx;
        font-weight: bold;
      }

      > view:nth-child(2) {
        font-size: 26rpx;
        color: #2c6ff3;
        border: 1rpx solid #2c6ff3;
        border-radius: 6rpx;
        padding: 0 8rpx;
        background-color: rgb(243, 249, 525);
        margin-left: 12rpx;
      }
    }

    .t-right {
      font-size: 30rpx;
      color: green;
      position: relative;
      margin-left: 24rpx;

      &::before {
        content: "";
        display: inline-block;
        background-color: green;
        width: 16rpx;
        height: 16rpx;
        border-radius: 16rpx;
        margin-right: 12rpx;
      }

      &.off {
        color: #666;

        &::before {
          background-color: #666;
        }
      }
    }
  }

  > view:nth-child(2) {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-top: 12rpx;

    > .cu-btn {
      height: 40rpx;
    }

    .edit {
      font-size: 26rpx;
      color: #fff;
      border: 1rpx solid #2c6ff3;
      border-radius: 6rpx;
      padding: 4rpx 8rpx;
      background-color: rgb(243, 249, 525);
      margin-left: 12rpx;
      background-color: #2c6ff3;
    }
  }
}

.center {
  border-radius: 8rpx;
  background-color: rgb(245, 248, 251);
  padding: 24rpx 12rpx 42rpx;
  font-size: 30rpx;
  margin-top: 24rpx;
  overflow: hidden;
  position: relative;
  height: 600rpx;
  transition: all 0.5s ease 0s;

  .unfold {
    position: absolute;
    bottom: 8rpx;
    right: 24rpx;

    > text {
      line-height: 30rpx;
      color: #2c6ff3;
      font-size: 24rpx;
    }
  }

  &.center-more {
    height: 180rpx;
  }

  .d-line {
    display: flex;
    flex-flow: row nowrap;
    justify-content: flex-start;

    > view:nth-child(1) {
      width: 50%;
    }

    > view:nth-child(2) {
      width: 50%;
    }
  }

  .c-item {
    display: flex;
    flex-flow: row nowrap;
    justify-content: flex-start;
    font-size: 28rpx;
    margin-bottom: 18rpx;
    align-items: flex-end;

    .name {
      min-width: 100rpx;
      color: #999;
    }

    .val {
      color: #333;
      padding-left: 6rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      text {
        margin-left: 12rpx;
        text-decoration: underline;
        color: #2c6ff3;
      }

      &.net {
        text {
          text-decoration: none;
        }
      }

      & > view {
        width: 6rpx;
        background-color: #dadbde;
        margin-right: 4rpx;
        border-radius: 6rpx;
      }

      .dbm-green {
        background-color: #00ce00;
      }

      .dbm1 {
        height: 8rpx;
      }

      .dbm2 {
        height: 15rpx;
      }

      .dbm3 {
        height: 22rpx;
      }

      .dbm4 {
        height: 28rpx;
      }
    }
  }
}
</style>
