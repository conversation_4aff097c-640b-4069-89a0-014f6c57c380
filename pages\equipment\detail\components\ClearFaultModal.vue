<template>
  <xpopup
    :show="show"
    @close="handleClose"
    @confirm="handleConfirm"
    :showBtn="true"
    title="清除故障">
    <view class="popup-content restart"> 是否确定清除故障? </view>
  </xpopup>
</template>

<script>
import { sendCommand } from "@/api/device/device.js";
import getDict from "@/utils/getDict.js";

export default {
  name: "ClearFaultModal",
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    deviceId: {
      type: [String, Number],
      required: true,
    },
  },
  methods: {
    getDict,

    handleClose() {
      this.$emit("close");
    },

    async handleConfirm() {
      try {
        let templet = null;
        await this.getDict("mqtt_cmd_templet_task", "abort").then((res) => {
          templet = JSON.parse(res[0].value);
          templet.data.type = "abort";
          templet.data.task = "activity";
          templet.data.door = 0;
        });

        await sendCommand([
          {
            deviceId: this.deviceId,
            templet: templet,
          },
        ]);

        this.$modal.showToast("设置成功~");
        this.$modal.msg("清除故障指令已下发！");
        this.$emit("success");
        this.$emit("close");
      } catch (error) {
        console.error("清除故障失败:", error);
        this.$modal.showToast("清除故障失败，请重试");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.popup-content {
  padding: 0 24rpx;
}

.restart {
  padding: 40rpx 0;
  font-size: 34rpx;
  text-align: center;
}
</style>
