<template>
  <xpopup
    :show="show"
    @close="handleClose"
    @confirm="handleConfirm"
    :showBtn="true"
    title="告警设置">
    <view class="popup-content">
      <u--form
        labelPosition="left"
        :model="tempNot"
        :rules="tempNotRule"
        ref="tempNot">
        <view v-if="deviceDetail.deviceStatus && deviceDetail.deviceStatus.tempValue != '无温控仪'">
          <view class="not-tit">温度告警设置</view>
          <u-form-item
            label="最低告警温度(℃):"
            prop="tempMin"
            labelWidth="140">
            <u--input v-model="tempNot.tempMin"></u--input>
          </u-form-item>
          <u-form-item
            label="最高告警温度(℃):"
            prop="tempMax"
            labelWidth="140">
            <u--input v-model="tempNot.tempMax"></u--input>
          </u-form-item>
        </view>
        <view class="not-tit">库存告警设置</view>
        <u-form-item
          label="缺货率预警值(%):"
          prop="stockOutRate"
          labelWidth="140">
          <u--input v-model="tempNot.stockOutRate"></u--input>
        </u-form-item>
        <u-form-item
          label="缺货种类预警值(种):"
          prop="stockOutGoodsNum"
          labelWidth="140">
          <u--input v-model="tempNot.stockOutGoodsNum"></u--input>
        </u-form-item>
      </u--form>
    </view>
  </xpopup>
</template>

<script>
import { batchUpdate } from "@/api/device/device.js";

export default {
  name: "AlarmSettingModal",
  props: {
    show: {
      type: Boolean,
      default: false
    },
    deviceId: {
      type: [String, Number],
      required: true
    },
    deviceDetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tempNot: {
        tempMin: "",
        tempMax: "",
        stockOutRate: "",
        stockOutGoodsNum: "",
      },
      tempNotRule: {
        tempMin: [
          {
            required: true,
            message: "请输入最低告警温度",
            trigger: ["blur", "change"],
          },
        ],
        tempMax: [
          {
            required: true,
            message: "请输入最高告警温度",
            trigger: ["blur", "change"],
          },
        ],
        stockOutRate: [
          {
            required: true,
            message: "请输入缺货率预警值",
            trigger: ["blur", "change"],
          },
        ],
        stockOutGoodsNum: [
          {
            required: true,
            message: "请输入缺货种类预警值",
            trigger: ["blur", "change"],
          },
        ],
      }
    };
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.initData();
      }
    }
  },
  methods: {
    initData() {
      this.tempNot = {
        tempMin: this.deviceDetail.deviceConfig?.tempMin,
        tempMax: this.deviceDetail.deviceConfig?.tempMax,
        stockOutRate: this.deviceDetail.deviceConfig?.stockOutRate,
        stockOutGoodsNum: this.deviceDetail.deviceConfig?.stockOutGoodsNum,
      };
    },

    handleClose() {
      this.$emit('close');
    },

    async handleConfirm() {
      try {
        // 表单验证
        await this.$refs.tempNot.validate();
        
        if (this.tempNot.tempMax < this.tempNot.tempMin) {
          this.$modal.msg("最高温度不能低于最低温度！");
          return;
        }

        await batchUpdate({
          deviceIds: [this.deviceId],
          tempMax: this.tempNot.tempMax,
          tempMin: this.tempNot.tempMin,
          stockOutRate: this.tempNot.stockOutRate,
          stockOutGoodsNum: this.tempNot.stockOutGoodsNum,
        });
        
        this.$modal.showToast("设置成功~");
        this.$emit('success');
        this.$emit('close');
      } catch (error) {
        console.error('告警设置失败:', error);
        if (error.message) {
          this.$modal.msg(error.message);
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.popup-content {
  padding: 0 24rpx;

  .not-tit {
    font-size: 28rpx;
    font-weight: bold;
    text-align: center;
    line-height: 60rpx;
  }
}
</style>
