<template>
  <xpopup
    :show="show"
    @close="handleClose"
    @confirm="handleConfirm"
    :showBtn="true"
    title="灯箱设置">
    <view class="popup-content temp">
      <view
        class="flex align-center justify-between"
        style="margin-bottom: 24rpx">
        <view class="lab" style="margin: 0 24rpx 0 0">是否打开:</view>
        <u-switch v-model="adlightDetail.lightControl"></u-switch>
      </view>

      <view class="info">时段设置为X点至Y点，结束时间需大于起始时间</view>
      <view
        style="
          text-align: center;
          font-size: 28rpx;
          margin-top: 20rpx;
          font-weight: bold;
        ">
        主灯箱
      </view>
      <view class="flex align-center">
        <view class="lab">时段1:</view>
        <view
          class="flex align-center date-container marleft"
          style="padding-left: 10rpx; padding-right: 10rpx"
          @tap="pickerTimes(1)">
          <view class="time-box-l">
            {{ adlightDetail.start1 }}
          </view>
          <view class="lab to">至</view>
          <view class="time-box-l">{{ adlightDetail.end1 }}</view>
        </view>
      </view>
      <view class="flex align-center">
        <view class="lab">时段2:</view>
        <view
          class="flex align-center date-container marleft"
          style="padding-left: 10rpx; padding-right: 10rpx"
          @tap="pickerTimes(2)">
          <view class="time-box-l">
            {{ adlightDetail.start2 }}
          </view>
          <view class="lab to">至</view>
          <view class="time-box-l">{{ adlightDetail.end2 }}</view>
        </view>
      </view>
      <view class="flex align-center">
        <view class="lab">时段3:</view>
        <view
          class="flex align-center date-container marleft"
          style="padding-left: 10rpx; padding-right: 10rpx"
          @tap="pickerTimes(3)">
          <view class="time-box-l">
            {{ adlightDetail.start3 }}
          </view>
          <view class="lab to">至</view>
          <view class="time-box-l">{{ adlightDetail.end3 }}</view>
        </view>
      </view>

      <view
        style="
          text-align: center;
          font-size: 28rpx;
          margin-top: 30rpx;
          font-weight: bold;
        ">
        副灯箱
      </view>
      <view class="flex align-center">
        <view class="lab">时段1:</view>
        <view
          class="flex align-center date-container marleft"
          style="padding-left: 10rpx; padding-right: 10rpx"
          @tap="pickerTimes(4)">
          <view class="time-box-l">
            {{ adlightDetail.start4 }}
          </view>
          <view class="lab to">至</view>
          <view class="time-box-l">{{ adlightDetail.end4 }}</view>
        </view>
      </view>
      <view class="flex align-center">
        <view class="lab">时段2:</view>
        <view
          class="flex align-center date-container marleft"
          style="padding-left: 10rpx; padding-right: 10rpx"
          @tap="pickerTimes(5)">
          <view class="time-box-l">
            {{ adlightDetail.start5 }}
          </view>
          <view class="lab to">至</view>
          <view class="time-box-l">{{ adlightDetail.end5 }}</view>
        </view>
      </view>
      <view class="flex align-center">
        <view class="lab">时段3:</view>
        <view
          class="flex align-center date-container marleft"
          style="padding-left: 10rpx; padding-right: 10rpx"
          @tap="pickerTimes(6)">
          <view class="time-box-l">
            {{ adlightDetail.start6 }}
          </view>
          <view class="lab to">至</view>
          <view class="time-box-l">{{ adlightDetail.end6 }}</view>
        </view>
      </view>
      <view style="height: 20rpx"></view>
    </view>

    <!-- 时间选择器 -->
    <u-picker
      :show="showTimeSelPicker"
      ref="uPicker"
      :columns="times"
      @confirm="pickerTimeConfirm"
      closeOnClickOverlay
      @close="showTimeSelPicker = false"
      @cancel="showTimeSelPicker = false">
    </u-picker>
  </xpopup>
</template>

<script>
import { sendCommand, lastOne } from "@/api/device/device.js";
import getDict from "@/utils/getDict.js";

export default {
  name: "AdLightSettingModal",
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    deviceId: {
      type: [String, Number],
      required: true,
    },
  },
  data() {
    return {
      adlightDetail: {
        lightControl: true,
        start1: 0,
        end1: 0,
        start2: 0,
        end2: 0,
        start3: 0,
        end3: 0,
        start4: 0,
        end4: 0,
        start5: 0,
        end5: 0,
        start6: 0,
        end6: 0,
        curTimes: 0,
      },
      showTimeSelPicker: false,
      times: [
        [
          0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19,
          20, 21, 22, 23, 24,
        ],
        [
          0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19,
          20, 21, 22, 23, 24,
        ],
      ],
    };
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.loadAdLightSettings();
      }
    },
  },
  methods: {
    getDict,

    handleClose() {
      this.$emit("close");
    },

    async handleConfirm() {
      try {
        let templet = null;
        await this.getDict("mqtt_cmd_templet_sets", "adlight").then((res) => {
          templet = JSON.parse(res[0].value);
          templet.data.lightControl = this.adlightDetail.lightControl;
          templet.data.light0.start1 = this.adlightDetail.start1;
          templet.data.light0.end1 = this.adlightDetail.end1;
          templet.data.light0.start2 = this.adlightDetail.start2;
          templet.data.light0.end2 = this.adlightDetail.end2;
          templet.data.light0.start3 = this.adlightDetail.start3;
          templet.data.light0.end3 = this.adlightDetail.end3;
          templet.data.light1.start1 = this.adlightDetail.start4;
          templet.data.light1.end1 = this.adlightDetail.end4;
          templet.data.light1.start2 = this.adlightDetail.start5;
          templet.data.light1.end2 = this.adlightDetail.end5;
          templet.data.light1.start3 = this.adlightDetail.start6;
          templet.data.light1.end3 = this.adlightDetail.end6;
        });

        await sendCommand([
          {
            deviceId: this.deviceId,
            templet: templet,
          },
        ]);

        this.$modal.showToast("设置成功~");
        this.$emit("success");
        this.$emit("close");
      } catch (error) {
        console.error("灯箱设置失败:", error);
        this.$modal.showToast("设置失败，请重试");
      }
    },

    loadAdLightSettings() {
      lastOne({
        deviceId: this.deviceId,
        typeList: ["adlight"],
      })
        .then((res) => {
          if (res.data[0] && res.data[0].content) {
            let data = JSON.parse(res.data[0].content);
            this.adlightDetail = {
              lightControl: data.lightControl,
              start1: data.light0.start1,
              end1: data.light0.end1,
              start2: data.light0.start2,
              end2: data.light0.end2,
              start3: data.light0.start3,
              end3: data.light0.end3,
              start4: data.light1.start1,
              end4: data.light1.end1,
              start5: data.light1.start2,
              end5: data.light1.end2,
              start6: data.light1.start3,
              end6: data.light1.end3,
            };
          }
        })
        .catch(() => {
          // 加载失败时使用默认值
        });
    },

    pickerTimes(index) {
      this.adlightDetail.curTimes = index;
      this.showTimeSelPicker = true;
    },

    pickerTimeConfirm(e) {
      this.showTimeSelPicker = false;
      const index = this.adlightDetail.curTimes;

      // 特殊处理adlight，它的curTimes从1开始计数
      if (index === 1) {
        this.adlightDetail.start1 = e.value[0];
        this.adlightDetail.end1 = e.value[1];
      } else if (index === 2) {
        this.adlightDetail.start2 = e.value[0];
        this.adlightDetail.end2 = e.value[1];
      } else if (index === 3) {
        this.adlightDetail.start3 = e.value[0];
        this.adlightDetail.end3 = e.value[1];
      } else if (index === 4) {
        this.adlightDetail.start4 = e.value[0];
        this.adlightDetail.end4 = e.value[1];
      } else if (index === 5) {
        this.adlightDetail.start5 = e.value[0];
        this.adlightDetail.end5 = e.value[1];
      } else if (index === 6) {
        this.adlightDetail.start6 = e.value[0];
        this.adlightDetail.end6 = e.value[1];
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.popup-content {
  padding: 0 24rpx;
}

.temp {
  .info {
    color: red;
    height: 60rpx;
    line-height: 60rpx;
    text-align: center;
  }

  .flex {
    margin-top: 20rpx;
    height: 60rpx;

    .lab {
      line-height: 60rpx;
      height: 60rpx;
      text-align: center;
      font-weight: 600;
      margin-top: 20rpx;
    }

    .to {
      width: 60rpx;
      margin-left: 20rpx;
      margin-top: 0;
      font-weight: 500;
    }

    .time-box-l {
      height: 50rpx;
      width: 230rpx;
      line-height: 50rpx;
      margin-left: 12rpx;
      text-align: center;
      border-radius: 10rpx;
      border-style: solid;
      border-width: 1px;
      border-color: #dadbde;
    }
  }
}
</style>
