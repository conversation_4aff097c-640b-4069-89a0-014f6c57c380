<template>
  <xpopup
    :show="show"
    @close="handleClose"
    @confirm="handleConfirm"
    :showBtn="true"
    :title="title">
    <view class="popup-content restart"> 是否确定{{ btnState }}? </view>
  </xpopup>
</template>

<script>
import { modifyBusyStage, sendCommand } from "@/api/device/device.js";
import getDict from "@/utils/getDict.js";

export default {
  name: "BusinessStatusModal",
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    deviceId: {
      type: [String, Number],
      required: true,
    },
    currentBusyState: {
      type: Number,
      required: true,
    },
  },
  computed: {
    btnState() {
      return this.currentBusyState == 1 ? "暂停营业" : "开始营业";
    },
    title() {
      return this.btnState;
    },
  },
  methods: {
    getDict,

    handleClose() {
      this.$emit("close");
    },

    async handleConfirm() {
      try {
        await this.changeState();
        this.$emit("success");
        this.$emit("close");
      } catch (error) {
        console.error("营业状态变更失败:", error);
      }
    },

    async changeState() {
      let busyState = this.currentBusyState == 1 ? 2 : 1;
      const res = await modifyBusyStage({
        deviceId: this.deviceId,
        busyState: busyState,
      });

      if (res.code == 200) {
        let templet = "";
        await this.getDict("mqtt_cmd_templet_sets", "lockDevice").then(
          (res) => {
            templet = JSON.parse(res[0].value);
            templet.data.id = 0;
            templet.data.opt = busyState == 1 ? "unlock" : "lock";
          }
        );

        await sendCommand([
          {
            deviceId: this.deviceId,
            templet: templet,
          },
        ]);

        this.$modal.showToast("操作成功~");
        this.$emit("statusChanged", busyState);
        return true;
      } else {
        this.$modal.showToast("操作失败,请重试~");
        return false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.popup-content {
  padding: 0 24rpx;
}

.restart {
  padding: 40rpx 0;
  font-size: 34rpx;
  text-align: center;
}
</style>
