<template>
  <xpopup
    :show="show"
    @close="handleClose"
    @confirm="handleConfirm"
    :showBtn="true"
    title="声音设置">
    <view class="popup-content">
      <u-slider :showValue="true" min="0" max="25" v-model="voice"> </u-slider>
    </view>
  </xpopup>
</template>

<script>
import { sendCommand, lastOne } from "@/api/device/device.js";
import getDict from "@/utils/getDict.js";

export default {
  name: "VoiceSettingModal",
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    deviceId: {
      type: [String, Number],
      required: true,
    },
  },
  data() {
    return {
      voice: 4,
    };
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.loadVoiceSettings();
      }
    },
  },
  methods: {
    getDict,

    handleClose() {
      this.$emit("close");
    },

    async handleConfirm() {
      try {
        let templet = null;
        await this.getDict("mqtt_cmd_templet_sets", "deviceVoice").then(
          (res) => {
            templet = JSON.parse(res[0].value);
            templet.data.alound = this.voice;
          }
        );

        await sendCommand([
          {
            deviceId: this.deviceId,
            templet: templet,
          },
        ]);

        this.$modal.showToast("设置成功~");
        this.$emit("success");
        this.$emit("close");
      } catch (error) {
        console.error("声音设置失败:", error);
        this.$modal.showToast("设置失败，请重试");
      }
    },

    loadVoiceSettings() {
      lastOne({
        deviceId: this.deviceId,
        typeList: ["voice"],
      })
        .then((res) => {
          if (res.data[0] && res.data[0].content) {
            let data = JSON.parse(res.data[0].content);
            this.voice = data.alound;
          }
        })
        .catch(() => {
          // 加载失败时使用默认值
          this.voice = 4;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.popup-content {
  padding: 0 24rpx;
}
</style>
