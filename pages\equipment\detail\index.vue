<template>
  <view class="container">
    <u-navbar
      leftIconColor="#fff"
      titleStyle="color:#fff;fontSize:36rpx;"
      :autoBack="true"
      bgColor="#2C6FF3"
      :placeholder="true"
      title="设备详情">
    </u-navbar>
    <view class="content">
      <!-- 设备信息展示组件 -->
      <device-info-display
        :deviceDetail="detail"
        :deviceId="id"
        :activityState="activityState"
        :newVersion="newVersion"
        @edit="btnClick('编辑')"
        @upgrade="handleUpgrade">
      </device-info-display>

      <!-- 设备操作按钮组件 -->
      <device-operation-buttons
        :deviceDetail="detail"
        :btnState="btnState"
        @buttonClick="btnClick">
      </device-operation-buttons>

      <!-- 图表和统计数据组件 -->
      <device-statistics
        :deviceDetail="detail"
        :deviceId="id"
        :allCountData="allCountData"
        :tempChartData="tempChartData"
        :goodsManageData="goodsManageData"
        @tabChange="handleTabChange"
        @replenish="replenish">
      </device-statistics>

      <!-- 事件列表组件 -->
      <device-event-list :deviceId="id" ref="eventList"> </device-event-list>
    </view>

    <!-- 设备编辑弹框 -->
    <device-edit-modal
      :show="showModals.deviceEdit"
      :deviceId="id"
      :deviceDetail="detail"
      @close="closeModal('deviceEdit')"
      @success="handleDeviceEditSuccess">
    </device-edit-modal>

    <!-- 设备重启弹框 -->
    <device-restart-modal
      :show="showModals.deviceRestart"
      :deviceId="id"
      @close="closeModal('deviceRestart')"
      @success="handleSuccess">
    </device-restart-modal>

    <!-- 开侧门弹框 -->
    <open-side-door-modal
      :show="showModals.openSideDoor"
      :deviceId="id"
      @close="closeModal('openSideDoor')"
      @success="handleSuccess">
    </open-side-door-modal>

    <!-- 营业状态弹框 -->
    <business-status-modal
      :show="showModals.businessStatus"
      :deviceId="id"
      :currentBusyState="detail.busyState"
      @close="closeModal('businessStatus')"
      @success="handleSuccess"
      @statusChanged="handleStatusChanged">
    </business-status-modal>

    <!-- 声音设置弹框 -->
    <voice-setting-modal
      :show="showModals.voiceSetting"
      :deviceId="id"
      @close="closeModal('voiceSetting')"
      @success="handleSuccess">
    </voice-setting-modal>

    <!-- 告警设置弹框 -->
    <alarm-setting-modal
      :show="showModals.alarmSetting"
      :deviceId="id"
      :deviceDetail="detail"
      @close="closeModal('alarmSetting')"
      @success="handleAlarmSettingSuccess">
    </alarm-setting-modal>

    <!-- 温度设置弹框 -->
    <temperature-setting-modal
      :show="showModals.temperatureSetting"
      :deviceId="id"
      @close="closeModal('temperatureSetting')"
      @success="handleSuccess">
    </temperature-setting-modal>

    <!-- 灯光设置弹框 -->
    <light-setting-modal
      :show="showModals.lightSetting"
      :deviceId="id"
      @close="closeModal('lightSetting')"
      @success="handleSuccess">
    </light-setting-modal>

    <!-- 灯箱设置弹框 -->
    <ad-light-setting-modal
      :show="showModals.adLightSetting"
      :deviceId="id"
      @close="closeModal('adLightSetting')"
      @success="handleSuccess">
    </ad-light-setting-modal>

    <!-- 清除故障弹框 -->
    <clear-fault-modal
      :show="showModals.clearFault"
      :deviceId="id"
      @close="closeModal('clearFault')"
      @success="handleSuccess">
    </clear-fault-modal>

    <!-- 蓝牙授权弹框 -->
    <bluetooth-auth-modal
      :show="showModals.bluetoothAuth"
      :deviceDetail="detail"
      @close="closeModal('bluetoothAuth')"
      @success="handleSuccess">
    </bluetooth-auth-modal>

    <!-- 换绑SN弹框 -->
    <sn-binding-modal
      :show="showModals.snBinding"
      :deviceId="id"
      :deviceDetail="detail"
      @close="closeModal('snBinding')"
      @success="handleSnBindingSuccess">
    </sn-binding-modal>

    <!-- 加热丝设置弹框 -->
    <heater-setting-modal
      :show="showModals.heaterSetting"
      :deviceId="id"
      @close="closeModal('heaterSetting')"
      @success="handleSuccess">
    </heater-setting-modal>

    <!-- 添加升级弹框 -->
    <software-upgrade
      :show.sync="upgradePopupShow"
      :deviceId="id"
      :deviceSn="detail.sn"
      :currentVersion="detail.deviceSysinfo.appUpmVersion"
      :newVersion="newVersion"
      :upgradeUrl="upgradeUrl"
      :appId="appId"
      :appKey="appKey"></software-upgrade>

    <u-back-top :scroll-top="scrollTop" top="400"></u-back-top>
  </view>
</template>

<script>
import {
  detail,
  dataCount,
  deviceActiveState,
  queryUpInfoTask,
} from "@/api/device/device.js";
import { devicePart } from "@/api/replenishment/replenishment.js";
import SoftwareUpgrade from "./components/SoftwareUpgrade.vue";
import DeviceInfoDisplay from "./components/DeviceInfoDisplay.vue";
import DeviceOperationButtons from "./components/DeviceOperationButtons.vue";
import DeviceStatistics from "./components/DeviceStatistics.vue";
import DeviceEventList from "./components/DeviceEventList.vue";
import DeviceEditModal from "./components/DeviceEditModal.vue";
import DeviceRestartModal from "./components/DeviceRestartModal.vue";
import OpenSideDoorModal from "./components/OpenSideDoorModal.vue";
import BusinessStatusModal from "./components/BusinessStatusModal.vue";
import VoiceSettingModal from "./components/VoiceSettingModal.vue";
import AlarmSettingModal from "./components/AlarmSettingModal.vue";
import TemperatureSettingModal from "./components/TemperatureSettingModal.vue";
import LightSettingModal from "./components/LightSettingModal.vue";
import AdLightSettingModal from "./components/AdLightSettingModal.vue";
import ClearFaultModal from "./components/ClearFaultModal.vue";
import BluetoothAuthModal from "./components/BluetoothAuthModal.vue";
import SnBindingModal from "./components/SnBindingModal.vue";
import HeaterSettingModal from "./components/HeaterSettingModal.vue";

export default {
  components: {
    SoftwareUpgrade,
    DeviceInfoDisplay,
    DeviceOperationButtons,
    DeviceStatistics,
    DeviceEventList,
    DeviceEditModal,
    DeviceRestartModal,
    OpenSideDoorModal,
    BusinessStatusModal,
    VoiceSettingModal,
    AlarmSettingModal,
    TemperatureSettingModal,
    LightSettingModal,
    AdLightSettingModal,
    ClearFaultModal,
    BluetoothAuthModal,
    SnBindingModal,
    HeaterSettingModal,
  },
  data() {
    return {
      activityState: "",
      newVersion: null,
      upgradeUrl: null,
      appId: null,
      appKey: null,
      tempChartData: {
        series: [],
        categories: [],
      },
      // 弹框显示状态管理
      showModals: {
        deviceEdit: false,
        deviceRestart: false,
        openSideDoor: false,
        businessStatus: false,
        voiceSetting: false,
        alarmSetting: false,
        temperatureSetting: false,
        lightSetting: false,
        adLightSetting: false,
        clearFault: false,
        bluetoothAuth: false,
        snBinding: false,
        heaterSetting: false,
      },
      tempStatus: [
        {
          value: 2,
          label: "温控仪检测失败",
        },
        {
          value: 3,
          label: "温控仪故障",
        },
        {
          value: 120,
          label: "温控仪检测失败",
        },
        {
          value: 170,
          label: "未读取到温度",
        },
        {
          value: 161,
          label: "温控仪通讯故障",
        },
        {
          value: 162,
          label: "温控仪故障",
        },
        {
          value: 255,
          label: "温控仪检测失败",
        },
      ],
      id: null, //设备id
      detail: {}, //设备详情
      allCountData: {}, //经营统计数据显示
      scrollTop: 0, //滚动距离顶部
      goodsManageData: {
        categoryNum: 0,
        stock: 0,
        afterFillStock: 0,
      },
      upgradePopupShow: false, // 控制升级弹框显示
    };
  },

  mounted() {
    this.getDetail();
    this.getCountData();
    this.getGoodsManageData();
    this.getActivityState();
    this.getTempChartData();
  },

  computed: {
    btnState() {
      let str = this.detail.busyState == 1 ? "暂停营业" : "开始营业";
      return str;
    },
  },

  onLoad(o) {
    this.id = o.id;
    this.init();
  },

  methods: {
    async init() {
      //设备详情
      let detail = await this.getDetail();

      // 在加载设备详情后检查版本更新
      this.checkVersionUpdate();

      if (detail.deviceName == null) {
        //未设置名字，弹框设置
        this.btnClick("编辑");
      }

      this.getActivityState();

      // 经营数据
      this.allCountData = await this.getCountData(1);

      //商品管理数据
      let tempData = await this.getCountData(5);
      this.goodsManageData = tempData.goodsData;
    },

    updateTempStr() {
      if (!this.detail.deviceSysinfo.isHaveTemp) {
        this.detail.deviceStatus.tempValue = "无温控仪";
      } else {
        //如果温度异常，则转化为异常信息
        for (let i = 0; i < this.tempStatus.length; i++) {
          if (this.detail.deviceStatus.tempState == this.tempStatus[i].value) {
            this.detail.deviceStatus.tempValue = this.tempStatus[i].label;
            return;
          }
        }
        this.detail.deviceStatus.tempValue += "℃";
      }
    },

    // 获取设备详情
    getDetail() {
      return new Promise((resolve, reject) => {
        detail({
          deviceId: this.id,
          isSysinfo: true,
          isStatus: true,
          isRegister: true,
        })
          .then((res) => {
            this.detail = res.data;
            this.updateTempStr();
            resolve(res.data);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },

    //获取统计数据
    getCountData(type) {
      return new Promise((resolve, reject) => {
        dataCount(
          {
            deviceId: this.id,
            type: type,
          },
          false
        )
          .then((res) => {
            resolve(res.data);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },

    // 获取商品管理数据
    async getGoodsManageData() {
      let tempData = await this.getCountData(5);
      this.goodsManageData = tempData.goodsData;
    },

    // 获取温度图表数据
    async getTempChartData() {
      let tempChartData = {};
      let data = await this.getCountData(3);
      tempChartData.series = data.temperatureChart
        ? data.temperatureChart.series
        : [];
      tempChartData.categories = data.temperatureChart
        ? data.temperatureChart.categories
        : [];
      this.tempChartData = JSON.parse(JSON.stringify(tempChartData));
    },

    // 处理图表选项卡切换
    async handleTabChange(tabName) {
      if (tabName == "经营数据") {
        this.allCountData = await this.getCountData(1);
      } else if (tabName == "温度图表") {
        let tempChartData = {};
        let data = await this.getCountData(3);
        tempChartData.series = data.temperatureChart
          ? data.temperatureChart.series
          : [];
        tempChartData.categories = data.temperatureChart
          ? data.temperatureChart.categories
          : [];
        this.tempChartData = JSON.parse(JSON.stringify(tempChartData));
      }
    },

    // 显示弹框
    async btnClick(e) {
      if (e == "电子秤校准") {
        this.$tab.navigateTo(`/pages/equipment/calibration?id=${this.id}`);
        return;
      }

      // 根据按钮类型显示对应的弹框
      const modalMap = {
        编辑: "deviceEdit",
        设备重启: "deviceRestart",
        开侧门: "openSideDoor",
        暂停营业: "businessStatus",
        开始营业: "businessStatus",
        声音设置: "voiceSetting",
        告警设置: "alarmSetting",
        温度设置: "temperatureSetting",
        灯光设置: "lightSetting",
        灯箱设置: "adLightSetting",
        清除故障: "clearFault",
        蓝牙授权: "bluetoothAuth",
        换绑SN: "snBinding",
        加热丝设置: "heaterSetting",
      };

      const modalKey = modalMap[e];
      if (modalKey) {
        this.showModals[modalKey] = true;
      }
    },

    // 关闭弹框
    closeModal(modalKey) {
      this.showModals[modalKey] = false;
    },

    // 通用成功处理
    handleSuccess() {
      // 可以在这里添加通用的成功处理逻辑
    },

    // 设备编辑成功处理
    handleDeviceEditSuccess() {
      this.getDetail(); // 重新获取设备详情
    },

    // 告警设置成功处理
    handleAlarmSettingSuccess() {
      setTimeout(() => {
        this.getDetail(); // 重新获取设备详情
      }, 1000);
    },

    // SN换绑成功处理
    handleSnBindingSuccess() {
      setTimeout(() => {
        this.getDetail(); // 重新获取设备详情以更新SN显示
      }, 1000);
    },

    // 营业状态变更处理
    handleStatusChanged(newBusyState) {
      this.detail.busyState = newBusyState;
    },

    //获取设备活动状态
    getActivityState() {
      deviceActiveState({
        deviceId: this.id,
      }).then((res) => {
        if (res.data && res.data.length > 0) {
          var aiDoor0 = res.data[0];
          if (aiDoor0.door == 1) {
            aiDoor0 = res.data[1];
          }

          if (aiDoor0.workType == 1) {
            this.activityState = "交易中";
          } else if (aiDoor0.workType == 2) {
            this.activityState = "正在补货";
          } else {
            this.activityState = "";
          }
        }
      });
    },

    // 检查版本更新
    async checkVersionUpdate() {
      try {
        const res = await queryUpInfoTask();
        if (res.code !== 200) return this.$modal.msg("检查版本更新失败~");
        if (!res.data || res.data.length == 0)
          return this.$modal.msg("暂无版本更新~");
        if (res.data && res.data.length > 0) {
          let updateInfo = res.data.find(
            (i) => i.appKey == "aiMachineXY_Nor_No" && i.enable
          );
          let currentVersion = this.detail.deviceSysinfo.appUpmVersion;
          if (
            updateInfo.version ===
            currentVersion.substring(currentVersion.indexOf(".") + 1)
          )
            return this.$modal.msg("当前已是最新版本~");
          this.newVersion = updateInfo.version;
          this.upgradeUrl = updateInfo.src;
          this.appId = updateInfo.appId;
          this.appKey = updateInfo.appKey;
        }
      } catch (error) {
        this.$modal.msg("检查版本更新失败");
      }
    },

    handleUpgrade() {
      this.upgradePopupShow = true;
    },

    async replenish() {
      let url = "";
      if (this.detail.deviceType == 6 || this.detail.deviceType == 7) {
        let isOpenGravity = await this.isOpenGravity(this.id); //是否开启重力识别
        url = isOpenGravity
          ? "/pages/replenish/replenishmentGra"
          : "/pages/replenish/replenishmentHomePage";
      } else {
        url = "/pages/replenish/replenishmentHomePage";
      }
      this.$tab.navigateTo(
        `${url}?id=${this.id}&deviceType=${this.detail.deviceType}`
      );
    },

    isOpenGravity(deviceId) {
      return new Promise((resolve, reject) => {
        devicePart({
          deviceId: deviceId,
          code: "es",
        })
          .then((res) => {
            let data = res.data;
            if (data && data.length > 0) {
              //value:{is:true,type:1}
              let value = JSON.parse(data[0].value);
              resolve(value.is);
            } else {
              resolve(false);
            }
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.upgrade-btn {
  margin-left: 10px;
  font-size: 12px;
  padding: 2px 8px;
  background-color: #007aff;
  color: #fff;
  border-radius: 4px;
  border: none;
}
.upgrade-status {
  margin-left: 10px;
  font-size: 12px;
  color: #666;
}

.container {
  .content {
    padding: 24rpx;

    .xy-card {
      margin-bottom: 24rpx;
    }
  }
}

// 为升级弹框添加样式
.upgrade-popup {
  padding: 30rpx;

  .upgrade-info {
    margin-bottom: 40rpx;

    .version-row {
      display: flex;
      margin-bottom: 20rpx;

      .version-label {
        width: 180rpx;
        color: #666;
      }

      .version-value {
        flex: 1;
        font-weight: bold;
      }
    }

    .progress-container {
      margin-top: 30rpx;

      .progress-bar {
        height: 20rpx;
        background-color: #f0f0f0;
        border-radius: 10rpx;
        overflow: hidden;

        .progress-inner {
          height: 100%;
          background-color: #2c6ff3;
        }
      }

      .progress-text {
        display: block;
        text-align: right;
        margin-top: 10rpx;
        font-size: 24rpx;
        color: #666;
      }
    }
  }

  .upgrade-actions {
    display: flex;
    justify-content: center;
  }
}
</style>
