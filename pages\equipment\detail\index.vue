<template>
  <view class="container">
    <u-navbar
      leftIconColor="#fff"
      titleStyle="color:#fff;fontSize:36rpx;"
      :autoBack="true"
      bgColor="#2C6FF3"
      :placeholder="true"
      title="设备详情">
    </u-navbar>
    <view class="content">
      <view class="xy-card">
        <view class="top">
          <view>
            <view class="t-left">
              <view v-if="detail.deviceName"
                >{{ detail.deviceName
                }}<text style="color: #666; font-size: 26rpx"
                  >({{ detail.deviceId }})</text
                ></view
              >
              <view v-else>{{ detail.deviceId }}</view>
            </view>

            <view class="flex" style="white-space: nowrap">
              <!-- 断电/在线/离线 -->
              <view
                :class="[detail.sysPower == 2 ? 't-right off' : 't-right']"
                v-if="detail.sysPower == 2">
                已断电
              </view>
              <view
                :class="[
                  detail.netStateName == '在线' ? 't-right' : 't-right off',
                ]"
                v-else>
                {{ detail.netStateName || "离线" }}
              </view>
              <view
                :class="[detail.busyState == 1 ? 't-right' : 't-right off']">
                {{ detail.busyState == 1 ? "运营中" : "已停运" }}
              </view>
            </view>
          </view>
          <view class="flex flex-between" style="margin-top: 24rpx">
            <view v-show="detail.deviceType != '5'">
              <xbutton
                v-show="activityState.length > 0"
                round="25rpx"
                padding="4rpx 10rpx"
                size="mini"
                style="margin-right: 12rpx"
                :bgColor="activityState == '异常' ? '#ff0000' : '#66CC00'"
                color="#fff"
                >{{ activityState }}</xbutton
              >
              <xbutton
                round="25rpx"
                padding="0 10rpx"
                size="mini"
                style="margin-right: 12rpx"
                :bgColor="
                  detail.deviceStatus.doorStateL == 2 ? '#66CC00' : '#FFCC33'
                "
                color="#fff">
                {{ detail.deviceStatus.doorStateL == 2 ? "已关门" : "已开门" }}
              </xbutton>
            </view>

            <view class="flex flex-between">
              <qr-code-downloader
                v-if="checkPermi(['devDetail:downQr'])"
                :deviceId="id"></qr-code-downloader>
              <xbutton
                size="mini"
                @click="btnClick('编辑')"
                v-if="checkPermi(['devDetail:edit'])"
                >编辑
              </xbutton>
            </view>
          </view>
        </view>
        <view :class="[isMore ? 'center center-more' : 'center']">
          <view class="d-line">
            <view class="c-item">
              <view class="name"> 编号： </view>
              <view class="val">
                {{ detail.deviceId || "-" }}
                <text
                  @click="copy(detail.deviceId)"
                  v-if="detail.mercDeviceCode"
                  >复制</text
                >
              </view>
            </view>
            <view class="c-item">
              <view class="name"> 商户： </view>
              <view class="val">
                {{ detail.mercName || "-" }}
              </view>
            </view>
          </view>
          <view class="d-line">
            <view class="c-item">
              <view class="name"> 温度： </view>
              <view class="val">
                {{ detail.deviceStatus.tempValue || "-" }}
              </view>
            </view>
            <view class="c-item" style="align-items: center">
              <view class="name"> 强度： </view>
              <view class="val net flex align-end">
                <view
                  :class="[
                    detail.deviceStatus.netDbm > -100
                      ? 'dbm1 dbm-green'
                      : 'dbm1',
                  ]"></view>
                <view
                  :class="[
                    detail.deviceStatus.netDbm > -88
                      ? 'dbm2 dbm-green'
                      : 'dbm2',
                  ]"></view>
                <view
                  :class="[
                    detail.deviceStatus.netDbm > -78
                      ? 'dbm3 dbm-green'
                      : 'dbm3',
                  ]"></view>
                <view
                  :class="[
                    detail.deviceStatus.netDbm > -55
                      ? 'dbm4 dbm-green'
                      : 'dbm4',
                  ]"></view>
              </view>
            </view>
          </view>

          <view class="c-item">
            <view class="name"> 设备位置： </view>
            <view class="val" @click="showPos" style="width: 120rpx">
              <u-icon slot="right" size="20" name="map"></u-icon>
            </view>
          </view>

          <view class="c-item">
            <view class="name"> 流量卡号： </view>
            <view class="val">
              {{ detail.deviceSysinfo.simIccid || "-"
              }}<text
                v-if="detail.deviceSysinfo.simIccid"
                @click="copy(detail.deviceSysinfo.simIccid)">
                复制</text
              >
            </view>
          </view>

          <view class="c-item">
            <view class="name"> 流量卡运营商： </view>
            <view class="val">
              {{ detail.deviceSysinfo.simIsp || "-" }}
            </view>
          </view>

          <view class="c-item">
            <view class="name"> 软件版本： </view>
            <view class="val">
              {{ detail.deviceSysinfo.appUpmVersion || "-" }}
              <text
                v-if="
                  newVersion &&
                  newVersion !== detail.deviceSysinfo.appUpmVersion
                "
                @click="handleUpgrade">
                立刻升级
              </text>
            </view>
          </view>
          <view class="c-item">
            <view class="name"> 最后更新时间： </view>
            <view class="val">
              {{ detail.deviceStatus.updateTime || "-" }}
            </view>
          </view>
          <view class="c-item">
            <view class="name"> 激活状态： </view>
            <view class="val" v-if="detail.activeState == 1"> 已激活 </view>
            <view class="val" v-else> 未激活 </view>
          </view>
          <view class="c-item">
            <view class="name"> 激活时间： </view>
            <view class="val">
              {{ detail.activeTime || "-" }}
            </view>
          </view>
          <view class="c-item">
            <view class="name"> 区域： </view>
            <view class="val">
              {{ detail.districtName || "-" }}
            </view>
          </view>
          <view class="c-item">
            <view class="name"> 点位： </view>
            <view class="val">
              {{ detail.placeName || "-" }}
            </view>
          </view>
          <view class="unfold flex align-center" @click="isMore = !isMore">
            <text>{{ isMore ? "更多信息" : "收起" }}</text>
            <u-icon
              :name="isMore ? 'arrow-down' : 'arrow-up'"
              color="#2C6FF3"
              size="16"></u-icon>
          </view>
        </view>

        <view class="btn-wrap" v-if="checkPermi(['devDetail:setting'])">
          <view
            class="grid"
            :class="[btnShowMore ? 'btn-box btn-box-more' : 'btn-box']">
            <view class="btn-item" v-if="checkPermi(['devDetail:reload'])">
              <xbutton
                bgColor="red"
                width="140rpx"
                @click="btnClick('设备重启')"
                >设备重启</xbutton
              >
            </view>

            <view class="btn-item" v-if="checkPermi(['devDetail:voice'])">
              <xbutton
                bgColor="#fff"
                borderColor="#2C6FF3"
                color="#2C6FF3"
                width="140rpx"
                @click="btnClick('声音设置')"
                >声音设置</xbutton
              >
            </view>
            <view
              class="btn-item"
              v-if="
                checkPermi(['devDetail:voice']) &&
                detail.deviceStatus &&
                detail.deviceStatus.tempValue != '无温控仪'
              ">
              <xbutton
                bgColor="#fff"
                borderColor="#2C6FF3"
                color="#2C6FF3"
                width="140rpx"
                @click="btnClick('温度设置')"
                >温度设置</xbutton
              >
            </view>
            <view class="btn-item" v-if="checkPermi(['devDetail:light'])">
              <xbutton
                bgColor="#fff"
                borderColor="#2C6FF3"
                color="#2C6FF3"
                width="140rpx"
                @click="btnClick('灯光设置')"
                >灯光设置</xbutton
              >
            </view>

            <view class="btn-item" v-if="checkPermi(['devDetail:adlight'])">
              <xbutton
                bgColor="#fff"
                borderColor="#2C6FF3"
                color="#2C6FF3"
                width="140rpx"
                @click="btnClick('灯箱设置')"
                >灯箱设置</xbutton
              >
            </view>

            <view class="btn-item" v-if="checkPermi(['devDetail:clear'])">
              <xbutton
                bgColor="#fff"
                borderColor="#2C6FF3"
                color="#2C6FF3"
                width="140rpx"
                @click="btnClick('清除故障')"
                >清除故障</xbutton
              >
            </view>
            <view class="btn-item" v-if="checkPermi(['devDetail:batch'])">
              <xbutton
                bgColor="#fff"
                borderColor="#2C6FF3"
                color="#2C6FF3"
                width="140rpx"
                @click="btnClick('告警设置')"
                >告警设置</xbutton
              >
            </view>
            <view class="btn-item" v-if="checkPermi(['devDetail:blueTee'])">
              <xbutton
                bgColor="#fff"
                borderColor="#2C6FF3"
                color="#2C6FF3"
                width="140rpx"
                @click="btnClick('蓝牙授权')"
                >蓝牙授权</xbutton
              >
            </view>

            <view class="btn-item" v-if="checkPermi(['devDetail:stop'])">
              <xbutton width="140rpx" @click="btnClick(btnState)">{{
                btnState
              }}</xbutton>
            </view>

            <view
              class="btn-item"
              v-if="
                checkPermi(['devDetail:heaterStrip']) &&
                detail.deviceStatus &&
                detail.deviceStatus.tempValue != '无温控仪'
              ">
              <xbutton
                bgColor="#fff"
                borderColor="#2C6FF3"
                color="#2C6FF3"
                width="140rpx"
                @click="btnClick('加热丝设置')"
                >加热丝设置</xbutton
              >
            </view>
            <view class="btn-item" v-if="checkPermi(['devDetail:openDoor'])">
              <xbutton
                bgColor="#fff"
                borderColor="#2C6FF3"
                color="#2C6FF3"
                width="140rpx"
                @click="btnClick('开侧门')"
                >开侧门</xbutton
              >
            </view>
            <view class="btn-item" v-if="checkPermi(['devDetail:calibration'])">
              <xbutton
                bgColor="#fff"
                borderColor="#2C6FF3"
                color="#2C6FF3"
                width="140rpx"
                @click="btnClick('电子秤校准')"
                >电子秤校准</xbutton
              >
            </view>
            <view class="btn-item" v-if="checkPermi(['devDetail:bandSn'])">
              <xbutton
                bgColor="#fff"
                borderColor="#2C6FF3"
                color="#2C6FF3"
                width="140rpx"
                @click="btnClick('换绑SN')"
                >换绑SN</xbutton
              >
            </view>
          </view>

          <view
            class="btn-unfold flex align-center"
            @click="btnShowMore = !btnShowMore">
            <text>{{ btnShowMore ? "收起" : "更多设置" }}</text>
            <u-icon
              :name="btnShowMore ? 'arrow-up' : 'arrow-down'"
              color="#2C6FF3"
              size="16"></u-icon>
          </view>
        </view>
      </view>

      <view
        class="xy-card"
        style="
          margin-bottom: 0;
          border-bottom: 1rpx solid #f1f1f1;
          padding: 12rpx 0 24rpx;
        ">
        <u-tabs
          :list="chartTab"
          :scrollable="false"
          @click="tabClick"
          lineColor="#2C6FF3"></u-tabs>
      </view>
      <view class="xy-card">
        <view class="chart-content">
          <view class="total" v-if="current == '经营数据'">
            <view style="width: 200rpx">
              <u-subsection
                activeColor="#2C6FF3"
                :list="totalTab"
                :current="time"
                @change="totalChange">
              </u-subsection>
            </view>
            <view class="t-content">
              <view class="t-item">
                <view class="t-name"> 销售额 </view>
                <view class="t-num">
                  ￥<text>{{ $xy.delMoney(countData.salesMoney) }}</text>
                </view>
              </view>
              <view class="t-item">
                <view class="t-name"> 订单数 </view>
                <view class="t-num">
                  <text>{{ countData.salesCount || 0 }}</text>
                </view>
              </view>
              <view class="t-item">
                <view class="t-name"> 退款 </view>
                <view class="t-num">
                  ￥<text>{{ $xy.delMoney(countData.refundMoney) }}</text>
                </view>
              </view>
            </view>

            <view class="more-data">
              <xbutton
                width="140"
                @click="orderDetails"
                v-if="checkPermi(['devDetail:orderList'])">
                交易明细
              </xbutton>
              <xbutton
                style="margin-left: 20rpx"
                width="140"
                @click="more"
                v-if="checkPermi(['devDetail:salesData'])">
                销售统计
              </xbutton>
            </view>
          </view>
          <temperature-chart
            v-else-if="current == '温度图表'"
            :deviceDetail="detail"
            :chartData="tempChartData">
          </temperature-chart>
        </view>
      </view>

      <view class="xy-card">
        <view class="title"> 商品管理 </view>
        <view class="t-content">
          <view class="t-item">
            <view class="t-name"> 在售商品种类 </view>
            <view class="t-num">
              <text>{{ goodsManageData.categoryNum || 0 }}</text
              >种
            </view>
          </view>
          <view class="t-item">
            <view class="t-name"> 在售库存 </view>
            <view class="t-num">
              <text>{{ goodsManageData.stock || 0 }}</text>
            </view>
          </view>
          <view class="t-item">
            <view class="t-name"> 上次补货后库存 </view>
            <view class="t-num">
              <text>{{ goodsManageData.afterFillStock || 0 }}</text>
            </view>
          </view>
        </view>
        <view class="bot bot1">
          <view v-if="checkPermi(['devDetail:devComList'])">
            <xbutton
              bgColor="#fff"
              borderColor="#2C6FF3"
              color="#2C6FF3"
              width="140rpx"
              @click="
                $tab.navigateTo(
                  `/pages/equipment/comManage?id=${id}&deviceName=${detail.deviceName}`
                )
              ">
              管理商品</xbutton
            >
          </view>
          <view v-if="checkPermi(['devDetail:supplyRec'])">
            <xbutton
              bgColor="#fff"
              borderColor="#2C6FF3"
              color="#2C6FF3"
              width="140rpx"
              @click="
                $tab.navigateTo(`/pages/replenish/repRecord?deviceId=${id}`)
              "
              >补货记录</xbutton
            >
          </view>
          <view v-if="checkPermi(['devDetail:invRec'])">
            <xbutton
              bgColor="#fff"
              borderColor="#2C6FF3"
              color="#2C6FF3"
              width="140rpx"
              @click="
                $tab.navigateTo(
                  `/pages/replenish/physicalRecord?deviceId=${id}`
                )
              "
              >盘点记录</xbutton
            >
          </view>
          <view v-if="checkPermi(['devDetail:rep'])">
            <xbutton width="140rpx" @click="replenish"> 补货</xbutton>
          </view>
        </view>
      </view>

      <view class="xy-card">
        <view class="title"> 事件列表 </view>
        <scroll-view
          style="height: 410rpx"
          scroll-y
          lower-threshold="100"
          @scrolltolower="eventLoadMore">
          <view
            class="l-content"
            v-if="eventRecords && eventRecords.length > 0">
            <view class="l-item" v-for="item in eventRecords" :key="item.id">
              <view class="l-time">
                {{ item.createTime.substr(5) }}
              </view>
              <view class="l-status">
                {{ item.msg }}
              </view>
            </view>
          </view>
          <view class="l-content" v-else>
            <u-empty text="没有记录~"></u-empty>
          </view>
        </scroll-view>
      </view>

      <!-- 设备编辑弹框 -->
      <device-edit-modal
        :show="showModals.deviceEdit"
        :deviceId="id"
        :deviceDetail="detail"
        @close="closeModal('deviceEdit')"
        @success="handleDeviceEditSuccess">
      </device-edit-modal>

      <!-- 设备重启弹框 -->
      <device-restart-modal
        :show="showModals.deviceRestart"
        :deviceId="id"
        @close="closeModal('deviceRestart')"
        @success="handleSuccess">
      </device-restart-modal>

      <!-- 开侧门弹框 -->
      <open-side-door-modal
        :show="showModals.openSideDoor"
        :deviceId="id"
        @close="closeModal('openSideDoor')"
        @success="handleSuccess">
      </open-side-door-modal>

      <!-- 营业状态弹框 -->
      <business-status-modal
        :show="showModals.businessStatus"
        :deviceId="id"
        :currentBusyState="detail.busyState"
        @close="closeModal('businessStatus')"
        @success="handleSuccess"
        @statusChanged="handleStatusChanged">
      </business-status-modal>

      <!-- 声音设置弹框 -->
      <voice-setting-modal
        :show="showModals.voiceSetting"
        :deviceId="id"
        @close="closeModal('voiceSetting')"
        @success="handleSuccess">
      </voice-setting-modal>

      <!-- 告警设置弹框 -->
      <alarm-setting-modal
        :show="showModals.alarmSetting"
        :deviceId="id"
        :deviceDetail="detail"
        @close="closeModal('alarmSetting')"
        @success="handleAlarmSettingSuccess">
      </alarm-setting-modal>

      <!-- 温度设置弹框 -->
      <temperature-setting-modal
        :show="showModals.temperatureSetting"
        :deviceId="id"
        @close="closeModal('temperatureSetting')"
        @success="handleSuccess">
      </temperature-setting-modal>

      <!-- 灯光设置弹框 -->
      <light-setting-modal
        :show="showModals.lightSetting"
        :deviceId="id"
        @close="closeModal('lightSetting')"
        @success="handleSuccess">
      </light-setting-modal>

      <!-- 灯箱设置弹框 -->
      <ad-light-setting-modal
        :show="showModals.adLightSetting"
        :deviceId="id"
        @close="closeModal('adLightSetting')"
        @success="handleSuccess">
      </ad-light-setting-modal>

      <!-- 清除故障弹框 -->
      <clear-fault-modal
        :show="showModals.clearFault"
        :deviceId="id"
        @close="closeModal('clearFault')"
        @success="handleSuccess">
      </clear-fault-modal>

      <!-- 蓝牙授权弹框 -->
      <bluetooth-auth-modal
        :show="showModals.bluetoothAuth"
        :deviceDetail="detail"
        @close="closeModal('bluetoothAuth')"
        @success="handleSuccess">
      </bluetooth-auth-modal>

      <!-- 换绑SN弹框 -->
      <sn-binding-modal
        :show="showModals.snBinding"
        :deviceId="id"
        :deviceDetail="detail"
        @close="closeModal('snBinding')"
        @success="handleSnBindingSuccess">
      </sn-binding-modal>

      <!-- 加热丝设置弹框 -->
      <heater-setting-modal
        :show="showModals.heaterSetting"
        :deviceId="id"
        @close="closeModal('heaterSetting')"
        @success="handleSuccess">
      </heater-setting-modal>
    </view>

    <!-- 添加升级弹框 -->
    <software-upgrade
      :show.sync="upgradePopupShow"
      :deviceId="id"
      :deviceSn="detail.sn"
      :currentVersion="detail.deviceSysinfo.appUpmVersion"
      :newVersion="newVersion"
      :upgradeUrl="upgradeUrl"
      :appId="appId"
      :appKey="appKey"></software-upgrade>

    <u-back-top :scroll-top="scrollTop" top="400"></u-back-top>
  </view>
</template>

<script>
import {
  detail,
  dataCount,
  modifyBusyStage,
  deviceActiveState,
  eventList,
  queryUpInfoTask,
} from "@/api/device/device.js";
import { devicePart } from "@/api/replenishment/replenishment.js";
import SoftwareUpgrade from "./components/SoftwareUpgrade.vue";
import TemperatureChart from "./components/TemperatureChart.vue";
import QrCodeDownloader from "./components/QrCodeDownloader.vue";
import DeviceEditModal from "./components/DeviceEditModal.vue";
import DeviceRestartModal from "./components/DeviceRestartModal.vue";
import OpenSideDoorModal from "./components/OpenSideDoorModal.vue";
import BusinessStatusModal from "./components/BusinessStatusModal.vue";
import VoiceSettingModal from "./components/VoiceSettingModal.vue";
import AlarmSettingModal from "./components/AlarmSettingModal.vue";
import TemperatureSettingModal from "./components/TemperatureSettingModal.vue";
import LightSettingModal from "./components/LightSettingModal.vue";
import AdLightSettingModal from "./components/AdLightSettingModal.vue";
import ClearFaultModal from "./components/ClearFaultModal.vue";
import BluetoothAuthModal from "./components/BluetoothAuthModal.vue";
import SnBindingModal from "./components/SnBindingModal.vue";
import HeaterSettingModal from "./components/HeaterSettingModal.vue";

export default {
  components: {
    SoftwareUpgrade,
    TemperatureChart,
    QrCodeDownloader,
    DeviceEditModal,
    DeviceRestartModal,
    OpenSideDoorModal,
    BusinessStatusModal,
    VoiceSettingModal,
    AlarmSettingModal,
    TemperatureSettingModal,
    LightSettingModal,
    AdLightSettingModal,
    ClearFaultModal,
    BluetoothAuthModal,
    SnBindingModal,
    HeaterSettingModal,
  },
  data() {
    return {
      btnShowMore: false,
      activityState: "",
      newVersion: null,
      upgradeUrl: null,
      appId: null,
      appKey: null,
      current: "经营数据",
      chartTab: [
        {
          name: "经营数据",
        },
        {
          name: "温度图表",
        },
      ],
      tempChartData: {
        series: [],
        categories: [],
      },
      // 弹框显示状态管理
      showModals: {
        deviceEdit: false,
        deviceRestart: false,
        openSideDoor: false,
        businessStatus: false,
        voiceSetting: false,
        alarmSetting: false,
        temperatureSetting: false,
        lightSetting: false,
        adLightSetting: false,
        clearFault: false,
        bluetoothAuth: false,
        snBinding: false,
        heaterSetting: false,
      },
      tempStatus: [
        {
          value: 2,
          label: "温控仪检测失败",
        },
        {
          value: 3,
          label: "温控仪故障",
        },
        {
          value: 120,
          label: "温控仪检测失败",
        },
        {
          value: 170,
          label: "未读取到温度",
        },
        {
          value: 161,
          label: "温控仪通讯故障",
        },
        {
          value: 162,
          label: "温控仪故障",
        },
        {
          value: 255,
          label: "温控仪检测失败",
        },
      ],
      totalTab: ["今日", "本月"],
      time: 0,
      id: null, //设备id
      detail: {}, //设备详情

      allCountData: {}, //经营统计数据显示
      scrollTop: 0, //滚动距离顶部
      eventRecords: [], //事件列表
      loadmoreStatus: "loadmore", //加载更多状态
      isMore: true,

      goodsManageData: {
        categoryNum: 0,
        stock: 0,
        afterFillStock: 0,
      },

      countData: {
        salesMoney: 0,
        salesCount: 0,
        refundMoney: 0,
      },

      eventPage: 1,
      upgradePopupShow: false, // 控制升级弹框显示
    };
  },

  mounted() {
    this.getDetail();
    this.getDeviceEventRecords();
    this.getCountData();
    this.getGoodsManageData();
    this.getActivityState();
    this.getTempChartData();
  },

  computed: {
    btnState() {
      let str = this.detail.busyState == 1 ? "暂停营业" : "开始营业";
      return str;
    },
  },

  onLoad(o) {
    this.id = o.id;
    this.init();
  },

  methods: {
    async init() {
      //设备详情
      let detail = await this.getDetail();

      // 在加载设备详情后检查版本更新
      this.checkVersionUpdate();

      if (detail.deviceName == null) {
        //未设置名字，弹框设置
        this.btnClick("编辑");
      }

      this.getActivityState();

      // 经营数据
      this.allCountData = await this.getCountData(1);
      this.countData = this.allCountData.dayBusinessData;

      //商品管理数据
      let tempData = await this.getCountData(5);
      this.goodsManageData = tempData.goodsData;
    },

    updateTempStr() {
      if (!this.detail.deviceSysinfo.isHaveTemp) {
        this.detail.deviceStatus.tempValue = "无温控仪";
      } else {
        //如果温度异常，则转化为异常信息
        for (let i = 0; i < this.tempStatus.length; i++) {
          if (this.detail.deviceStatus.tempState == this.tempStatus[i].value) {
            this.detail.deviceStatus.tempValue = this.tempStatus[i].label;
            return;
          }
        }
        this.detail.deviceStatus.tempValue += "℃";
      }
    },

    // 获取设备详情
    getDetail() {
      return new Promise((resolve, reject) => {
        detail({
          deviceId: this.id,
          isSysinfo: true,
          isStatus: true,
          isRegister: true,
        })
          .then((res) => {
            this.detail = res.data;
            this.updateTempStr();
            resolve(res.data);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },

    //获取统计数据
    getCountData(type) {
      return new Promise((resolve, reject) => {
        dataCount(
          {
            deviceId: this.id,
            type: type,
          },
          false
        )
          .then((res) => {
            resolve(res.data);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },

    // 获取商品管理数据
    async getGoodsManageData() {
      let tempData = await this.getCountData(5);
      this.goodsManageData = tempData.goodsData;
    },

    // 获取温度图表数据
    async getTempChartData() {
      let tempChartData = {};
      let data = await this.getCountData(3);
      tempChartData.series = data.temperatureChart
        ? data.temperatureChart.series
        : [];
      tempChartData.categories = data.temperatureChart
        ? data.temperatureChart.categories
        : [];
      this.tempChartData = JSON.parse(JSON.stringify(tempChartData));
    },

    // 复制文字
    copy(e) {
      uni.setClipboardData({
        data: e,
      });
    },

    // tab切换
    async tabClick(e) {
      this.current = e.name;
      if (e.name == "经营数据") {
        this.allCountData = await this.getCountData(1);
      } else if (e.name == "温度图表") {
        let tempChartData = {};
        let data = await this.getCountData(3);
        tempChartData.series = data.temperatureChart
          ? data.temperatureChart.series
          : [];
        tempChartData.categories = data.temperatureChart
          ? data.temperatureChart.categories
          : [];
        this.tempChartData = JSON.parse(JSON.stringify(tempChartData));
      }
    },

    // 今日/本月切换
    totalChange(e) {
      this.time = e;
      if (this.time == 0) {
        //今日
        this.countData = this.allCountData.dayBusinessData;
      } else {
        //今月
        this.countData = this.allCountData.monthBusinessData;
      }
    },

    //销量更多数据
    more() {
      let deviceName = this.detail.deviceName
        ? this.detail.deviceName
        : this.detail.deviceId;
      this.$tab.navigateTo(
        `/pages/equipment/statistics?title=${deviceName}&id=${this.id}`
      );
    },

    orderDetails() {
      let deviceName = this.detail.deviceName;
      this.$tab.navigateTo(
        `/pages/order/deviceOrderList?name=${deviceName}&id=${this.detail.deviceId}`
      );
    },

    // 显示弹框
    async btnClick(e) {
      if (e == "电子秤校准") {
        this.$tab.navigateTo(`/pages/equipment/calibration?id=${this.id}`);
        return;
      }

      // 根据按钮类型显示对应的弹框
      const modalMap = {
        编辑: "deviceEdit",
        设备重启: "deviceRestart",
        开侧门: "openSideDoor",
        暂停营业: "businessStatus",
        开始营业: "businessStatus",
        声音设置: "voiceSetting",
        告警设置: "alarmSetting",
        温度设置: "temperatureSetting",
        灯光设置: "lightSetting",
        灯箱设置: "adLightSetting",
        清除故障: "clearFault",
        蓝牙授权: "bluetoothAuth",
        换绑SN: "snBinding",
        加热丝设置: "heaterSetting",
      };

      const modalKey = modalMap[e];
      if (modalKey) {
        this.showModals[modalKey] = true;
      }
    },

    // 关闭弹框
    closeModal(modalKey) {
      this.showModals[modalKey] = false;
    },

    // 通用成功处理
    handleSuccess() {
      // 可以在这里添加通用的成功处理逻辑
    },

    // 设备编辑成功处理
    handleDeviceEditSuccess() {
      this.getDetail(); // 重新获取设备详情
    },

    // 告警设置成功处理
    handleAlarmSettingSuccess() {
      setTimeout(() => {
        this.getDetail(); // 重新获取设备详情
      }, 1000);
    },

    // SN换绑成功处理
    handleSnBindingSuccess() {
      setTimeout(() => {
        this.getDetail(); // 重新获取设备详情以更新SN显示
      }, 1000);
    },

    // 营业状态变更处理
    handleStatusChanged(newBusyState) {
      this.detail.busyState = newBusyState;
    },

    //获取设备事件列表
    getDeviceEventRecords() {
      eventList({
        deviceId: this.id,
        page: {
          current: this.eventPage,
          size: 10,
        },
      }).then((res) => {
        let data = res.data.records;
        this.loadmoreStatus = data.length < 10 ? "nomore" : "loadmore";
        this.eventRecords = this.eventRecords.concat(data);
      });
    },

    //事件加载更多
    eventLoadMore() {
      if (this.loadmoreStatus === "nomore") return;
      this.eventPage++;
      this.getDeviceEventRecords();
    },

    showPos() {
      if (this.detail.lat && this.detail.lon) {
        uni.openLocation({
          //​使用微信内置地图查看位置。
          latitude: Number(this.detail.lat), //要去的纬度-地址
          longitude: Number(this.detail.lon), //要去的经度-地址
          name: this.detail.deviceName || this.detail.deviceId,
          address: this.detail.placeName || "-",
          success: () => {
            getApp().globalData.isOnShow = false;
          },
        });
      } else {
        this.$modal.msg("请先编辑设备位置信息~");
      }
    },

    //获取设备活动状态
    getActivityState() {
      deviceActiveState({
        deviceId: this.id,
      }).then((res) => {
        if (res.data && res.data.length > 0) {
          var aiDoor0 = res.data[0];
          if (aiDoor0.door == 1) {
            aiDoor0 = res.data[1];
          }

          if (aiDoor0.workType == 1) {
            this.activityState = "交易中";
          } else if (aiDoor0.workType == 2) {
            this.activityState = "正在补货";
          } else {
            this.activityState = "";
          }
        }
      });
    },

    // 检查版本更新
    async checkVersionUpdate() {
      try {
        const res = await queryUpInfoTask();
        if (res.code !== 200) return this.$modal.msg("检查版本更新失败~");
        if (!res.data || res.data.length == 0)
          return this.$modal.msg("暂无版本更新~");
        if (res.data && res.data.length > 0) {
          let updateInfo = res.data.find(
            (i) => i.appKey == "aiMachineXY_Nor_No" && i.enable
          );
          let currentVersion = this.detail.deviceSysinfo.appUpmVersion;
          if (
            updateInfo.version ===
            currentVersion.substring(currentVersion.indexOf(".") + 1)
          )
            return this.$modal.msg("当前已是最新版本~");
          this.newVersion = updateInfo.version;
          this.upgradeUrl = updateInfo.src;
          this.appId = updateInfo.appId;
          this.appKey = updateInfo.appKey;
        }
      } catch (error) {
        this.$modal.msg("检查版本更新失败");
      }
    },

    handleUpgrade() {
      this.upgradePopupShow = true;
    },

    async replenish() {
      let url = "";
      if (this.detail.deviceType == 6 || this.detail.deviceType == 7) {
        let isOpenGravity = await this.isOpenGravity(this.id); //是否开启重力识别
        url = isOpenGravity
          ? "/pages/replenish/replenishmentGra"
          : "/pages/replenish/replenishmentHomePage";
      } else {
        url = "/pages/replenish/replenishmentHomePage";
      }
      this.$tab.navigateTo(
        `${url}?id=${this.id}&deviceType=${this.detail.deviceType}`
      );
    },

    isOpenGravity(deviceId) {
      return new Promise((resolve, reject) => {
        devicePart({
          deviceId: deviceId,
          code: "es",
        })
          .then((res) => {
            let data = res.data;
            if (data && data.length > 0) {
              //value:{is:true,type:1}
              let value = JSON.parse(data[0].value);
              resolve(value.is);
            } else {
              resolve(false);
            }
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.upgrade-btn {
  margin-left: 10px;
  font-size: 12px;
  padding: 2px 8px;
  background-color: #007aff;
  color: #fff;
  border-radius: 4px;
  border: none;
}
.upgrade-status {
  margin-left: 10px;
  font-size: 12px;
  color: #666;
}

.container {
  .content {
    padding: 24rpx;

    .xy-card {
      margin-bottom: 24rpx;
    }

    .top {
      > view:nth-child(1) {
        display: flex;
        flex-flow: row nowrap;
        justify-content: space-between;

        .t-left {
          width: 380rpx;

          > view:nth-child(1) {
            font-size: 36rpx;
            font-weight: bold;
          }

          > view:nth-child(2) {
            font-size: 26rpx;
            color: #2c6ff3;
            border: 1rpx solid #2c6ff3;
            border-radius: 6rpx;
            padding: 0 8rpx;
            background-color: rgb(243, 249, 525);
            margin-left: 12rpx;
          }
        }

        .t-right {
          font-size: 30rpx;
          color: green;
          position: relative;
          margin-left: 24rpx;

          &::before {
            content: "";
            display: inline-block;
            background-color: green;
            width: 16rpx;
            height: 16rpx;
            border-radius: 16rpx;
            margin-right: 12rpx;
          }

          &.off {
            color: #666;

            &::before {
              background-color: #666;
            }
          }
        }
      }

      > view:nth-child(2) {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        margin-top: 12rpx;

        > .cu-btn {
          height: 40rpx;
        }

        .edit {
          font-size: 26rpx;
          color: #fff;
          border: 1rpx solid #2c6ff3;
          border-radius: 6rpx;
          padding: 4rpx 8rpx;
          background-color: rgb(243, 249, 525);
          margin-left: 12rpx;
          background-color: #2c6ff3;
        }
      }
    }

    .center {
      border-radius: 8rpx;
      background-color: rgb(245, 248, 251);
      padding: 24rpx 12rpx 42rpx;
      font-size: 30rpx;
      margin-top: 24rpx;
      overflow: hidden;
      position: relative;
      height: 600rpx;
      transition: all 0.5s ease 0s;

      .unfold {
        position: absolute;
        bottom: 8rpx;
        right: 24rpx;

        > text {
          line-height: 30rpx;
          color: #2c6ff3;
          font-size: 24rpx;
        }
      }

      &.center-more {
        height: 180rpx;
      }

      .d-line {
        display: flex;
        flex-flow: row nowrap;
        justify-content: flex-start;

        > view:nth-child(1) {
          width: 50%;
        }

        > view:nth-child(2) {
          width: 50%;
        }
      }

      .c-item {
        display: flex;
        flex-flow: row nowrap;
        justify-content: flex-start;
        font-size: 28rpx;
        margin-bottom: 18rpx;
        align-items: flex-end;

        .name {
          min-width: 100rpx;
          color: #999;
        }

        .val {
          color: #333;
          padding-left: 6rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          text {
            margin-left: 12rpx;
            text-decoration: underline;
            color: #2c6ff3;
          }

          &.net {
            text {
              text-decoration: none;
            }
          }

          & > view {
            width: 6rpx;
            background-color: #dadbde;
            margin-right: 4rpx;
            border-radius: 6rpx;
          }

          .dbm-green {
            background-color: #00ce00;
          }

          .dbm1 {
            height: 8rpx;
          }

          .dbm2 {
            height: 15rpx;
          }

          .dbm3 {
            height: 22rpx;
          }

          .dbm4 {
            height: 28rpx;
          }
        }
      }
    }

    .btn-wrap {
      position: relative;
      padding-bottom: 60rpx;
      background-color: #fff;

      .btn-box {
        position: relative;
        height: 100rpx;
        transition: all 0.5s ease 0s;
        overflow: hidden;
        column-gap: 30rpx;

        &.btn-box-more {
          height: auto;
        }

        .btn-item {
          margin-top: 24rpx;
        }
      }

      .btn-unfold {
        height: 40rpx;
        position: absolute;
        right: 24rpx;
        bottom: 0;

        > text {
          line-height: 30rpx;
          color: #2c6ff3;
          font-size: 24rpx;
        }
      }
    }

    .bot {
      display: flex;
      flex-flow: row nowrap;
      justify-content: space-between;
      margin-top: 24rpx;

      .cu-btn {
        padding: 0 12rpx;
        font-size: 26rpx;
        height: 60rpx;
        line-height: 50rpx;
      }

      .cu-btn1 {
        background-color: red;
        color: #fff;
      }

      .cu-btn2 {
        background-color: #fff;
        color: #2c6ff3;
        border: 1rpx solid #2c6ff3;
      }

      .cu-btn3 {
        background-color: #2c6ff3;
        color: #fff;
      }
    }

    .chart-content {
      width: 100%;

      .total {
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      .more {
        background-color: #2c6ff3;
        padding: 0 24rpx;
        font-size: 26rpx;
        height: 50rpx;
        line-height: 50rpx;
        margin-top: 24rpx;
      }
    }

    .t-content {
      display: flex;
      flex-flow: row nowrap;
      width: 100%;
      box-sizing: border-box;
      text-align: center;

      .t-item {
        width: 50%;
        margin-top: 24rpx;

        .t-name {
          font-size: 28rpx;
          line-height: 40rpx;
        }

        .t-num {
          font-size: 28rpx;
          line-height: 46rpx;
          padding: 12rpx 0;

          text {
            font-size: 40rpx;
            font-weight: bold;
          }
        }
      }
    }

    .title {
      font-size: 34rpx;
      font-weight: bold;
      color: #333;
      line-height: 54rpx;
    }

    .bot1 {
      display: flex;
      justify-content: flex-end;

      > view {
        margin-left: 24rpx;
      }
    }

    .l-content {
      padding-top: 24rpx;

      .l-item {
        font-size: 28rpx;
        line-height: 50rpx;
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        + .l-item {
          margin-top: 24rpx;
        }

        .l-time {
          width: 252rpx;
        }

        .l-status {
          width: 540rpx;
        }
      }
    }
  }
}

// 为升级弹框添加样式
.upgrade-popup {
  padding: 30rpx;

  .upgrade-info {
    margin-bottom: 40rpx;

    .version-row {
      display: flex;
      margin-bottom: 20rpx;

      .version-label {
        width: 180rpx;
        color: #666;
      }

      .version-value {
        flex: 1;
        font-weight: bold;
      }
    }

    .progress-container {
      margin-top: 30rpx;

      .progress-bar {
        height: 20rpx;
        background-color: #f0f0f0;
        border-radius: 10rpx;
        overflow: hidden;

        .progress-inner {
          height: 100%;
          background-color: #2c6ff3;
        }
      }

      .progress-text {
        display: block;
        text-align: right;
        margin-top: 10rpx;
        font-size: 24rpx;
        color: #666;
      }
    }
  }

  .upgrade-actions {
    display: flex;
    justify-content: center;
  }
}
</style>
